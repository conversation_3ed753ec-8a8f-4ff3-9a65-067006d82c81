"""
Test script for the inventory dashboard functions
"""

import pandas as pd
import numpy as np
from datetime import datetime

print("🧪 Testing Inventory Dashboard Functions...")

# Test basic data loading
print("\n📊 Testing basic data loading...")

try:
    # Load one data file to test
    df = pd.read_excel('data/merge_total.xlsx')
    print(f"✅ Data loaded successfully: {df.shape}")
    print(f"Columns: {list(df.columns[:5])}...")
    print(f"Date range: {df['Week'].min()} to {df['Week'].max()}")
    
    # Test basic statistics
    print("\n📈 Testing basic statistics...")
    df['week_of_year'] = df['Week'].dt.isocalendar().week
    df['year'] = df['Week'].dt.year
    
    # Test 5-year stats calculation
    column = 'Total Land + Water'
    if column in df.columns:
        stats = df.groupby('week_of_year')[column].agg(['min', 'max', 'mean']).reset_index()
        print(f"✅ 5-year stats calculated: {stats.shape}")
        print(f"Sample stats:\n{stats.head(3)}")
    
    # Test percentile calculation
    print("\n🌈 Testing percentile calculation...")
    def calc_percentile(group):
        if len(group) > 0:
            group['percentile'] = group[column].rank(pct=True) * 100
        return group
    
    df_with_percentiles = df.groupby('week_of_year').apply(calc_percentile).reset_index(drop=True)
    print(f"✅ Percentiles calculated: {df_with_percentiles.shape}")
    
    # Test anomaly detection
    print("\n🚩 Testing anomaly detection...")
    anomalies_high = df_with_percentiles['percentile'] >= 90
    anomalies_low = df_with_percentiles['percentile'] <= 10
    total_anomalies = anomalies_high.sum() + anomalies_low.sum()
    print(f"✅ Anomalies detected: {total_anomalies} total ({anomalies_high.sum()} high, {anomalies_low.sum()} low)")
    
    print("\n✅ All basic tests passed!")
    
except Exception as e:
    print(f"❌ Error during testing: {e}")
    import traceback
    traceback.print_exc()

print("\n🎯 Basic functionality verified. Ready to test full dashboard!")
