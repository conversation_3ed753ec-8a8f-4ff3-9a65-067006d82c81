"""
Test script to verify geography filtering works correctly
"""

from inventory_dashboard_functions import InventoryDataProcessor

print("🧪 Testing Geography Filtering")
print("=" * 40)

# Initialize processor
processor = InventoryDataProcessor()
processor.load_data()

# Test different geography filters
test_cases = [
    ('all', 'all', 'land'),
    ('all', 'us', 'land'),
    ('all', 'europe', 'land'),
    ('all', 'asia', 'land'),
    ('crude_oil', 'us', 'land'),
    ('total_products', 'europe', 'land'),
    ('gasoline', 'asia', 'land')
]

for product, geo, storage in test_cases:
    print(f"\n📊 Filter: Product={product}, Geography={geo}, Storage={storage}")
    
    try:
        df = processor.get_filtered_data(product, geo, storage)
        cols = [col for col in df.columns if col != 'Week']
        print(f"   ✅ Found {len(cols)} columns")
        
        if cols:
            print(f"   📋 Columns: {cols}")
        else:
            print("   ⚠️  No data columns found")
            
    except Exception as e:
        print(f"   ❌ Error: {e}")

print("\n✅ Geography filtering test complete!")
