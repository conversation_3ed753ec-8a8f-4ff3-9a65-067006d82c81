"""
Global Liquids Inventory Data Tracking Dashboard

A dynamic Dash application for tracking global liquids inventory data with:
- Interactive drill-down capabilities
- 5-year historical range visualization
- Week-of-year percentile ribbon
- Anomaly detection and flagging
"""

import dash
from dash import dcc, html, Input, Output, callback
import plotly.graph_objects as go
import pandas as pd
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Import our custom functions
from inventory_dashboard_functions import InventoryDataProcessor, create_main_chart, create_percentile_trend_chart, create_anomaly_flags, get_summary_stats

# Initialize the data processor with correct data path
processor = InventoryDataProcessor(data_path='data/')

# Initialize Dash app
app = dash.Dash(__name__)
app.title = "Global Liquids Inventory Dashboard"

# Define the layout
app.layout = html.Div([
    # Header
    html.Div([
        html.H1("🛢️ Global Liquids Inventory Tracking Dashboard", 
                style={'textAlign': 'center', 'color': '#2c3e50', 'marginBottom': '30px'}),
        html.P("Instantly see where today's stocks sit versus their historic seasonal range",
               style={'textAlign': 'center', 'fontSize': '18px', 'color': '#7f8c8d'}),
        html.P("🔮 Blue shading shows 2025 projection | 📊 Selected years show as comparison overlay lines",
               style={'textAlign': 'center', 'fontSize': '14px', 'color': '#3498db', 'fontStyle': 'italic'})
    ], style={'padding': '20px'}),
    
    # Control Panel
    html.Div([
        html.H3("📊 Drill-Down Controls", style={'color': '#34495e'}),
        
        # Row 1: Product Type and Geography
        html.Div([
            html.Div([
                html.Label("Product Type:", style={'fontWeight': 'bold'}),
                dcc.Dropdown(
                    id='product-type-dropdown',
                    options=[
                        {'label': 'All Products', 'value': 'all'},
                        {'label': 'Crude Oil', 'value': 'crude_oil'},
                        {'label': 'Total Products', 'value': 'total_products'},
                        {'label': 'Gasoline', 'value': 'gasoline'},
                        {'label': 'Distillates', 'value': 'distillates'},
                        {'label': 'Core Products', 'value': 'core_products'}
                    ],
                    value='all',
                    style={'marginTop': '5px'}
                )
            ], style={'width': '48%', 'display': 'inline-block', 'marginRight': '4%'}),
            
            html.Div([
                html.Label("Geography:", style={'fontWeight': 'bold'}),
                dcc.Dropdown(
                    id='geography-dropdown',
                    options=[
                        {'label': 'All Regions', 'value': 'all'},
                        {'label': 'Global/Total', 'value': 'global'},
                        {'label': 'United States', 'value': 'us'},
                        {'label': 'Europe (ARA)', 'value': 'europe'},
                        {'label': 'Asia (Singapore)', 'value': 'asia'}
                    ],
                    value='all',
                    style={'marginTop': '5px'}
                )
            ], style={'width': '48%', 'display': 'inline-block'})
        ], style={'marginBottom': '20px'}),
        
        # Row 2: Storage Type and Years
        html.Div([
            html.Div([
                html.Label("Storage Type:", style={'fontWeight': 'bold'}),
                dcc.Dropdown(
                    id='storage-type-dropdown',
                    options=[
                        {'label': 'All Storage', 'value': 'all'},
                        {'label': 'Land Storage', 'value': 'land'},
                        {'label': 'Oil on Water', 'value': 'water'},
                        {'label': 'Floating Storage', 'value': 'floating'}
                    ],
                    value='all',
                    style={'marginTop': '5px'}
                )
            ], style={'width': '48%', 'display': 'inline-block', 'marginRight': '4%'}),
            
            html.Div([
                html.Label("Years to Include:", style={'fontWeight': 'bold'}),
                dcc.Checklist(
                    id='years-checklist',
                    options=[
                        {'label': '2017', 'value': 2017},
                        {'label': '2018', 'value': 2018},
                        {'label': '2019', 'value': 2019},
                        {'label': '2020', 'value': 2020},
                        {'label': '2021', 'value': 2021},
                        {'label': '2022', 'value': 2022},
                        {'label': '2023', 'value': 2023},
                        {'label': '2024', 'value': 2024},
                        {'label': '2025', 'value': 2025}
                    ],
                    value=[datetime.now().year],  # Default to current year
                    inline=True,
                    style={'marginTop': '5px'}
                )
            ], style={'width': '48%', 'display': 'inline-block'})
        ], style={'marginBottom': '20px'}),
        
        # Column Selection
        html.Div([
            html.Label("Data Column:", style={'fontWeight': 'bold'}),
            dcc.Dropdown(
                id='column-dropdown',
                placeholder="Select a data column...",
                style={'marginTop': '5px'}
            )
        ], style={'marginBottom': '20px'})
        
    ], style={'padding': '20px', 'backgroundColor': '#ecf0f1', 'margin': '20px', 'borderRadius': '10px'}),
    
    # Summary Statistics
    html.Div(id='summary-stats', style={'padding': '20px', 'margin': '20px'}),
    
    # Main Chart
    html.Div([
        html.H3("📈 2025 Projection with Historical Comparison", style={'color': '#34495e'}),
        html.P("Light blue shading: 2025 projection range | Dotted lines: Selected years for comparison",
               style={'color': '#7f8c8d', 'fontStyle': 'italic', 'marginBottom': '15px'}),
        dcc.Graph(id='main-chart', style={'height': '500px'})
    ], style={'padding': '20px', 'margin': '20px', 'backgroundColor': '#ffffff', 'borderRadius': '10px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)'}),
    
    # Weekly Percentile Trend Chart
    html.Div([
        html.H3("📈 2025 Weekly Percentile Trend Analysis", style={'color': '#34495e'}),
        html.P("Shows where 2025 weekly inventory levels rank against historical patterns. Lower percentiles indicate below-average inventory levels.",
               style={'color': '#7f8c8d', 'fontStyle': 'italic', 'marginBottom': '15px'}),
        dcc.Graph(id='percentile-trend', style={'height': '400px'})
    ], style={'padding': '20px', 'margin': '20px', 'backgroundColor': '#ffffff', 'borderRadius': '10px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)'}),
    
    # Anomaly Flags
    html.Div([
        html.H3("🚩 Anomaly Flags", style={'color': '#34495e'}),
        html.P("Automatic highlighting when current week's level is in the top/bottom 10% of its 5-year distribution", 
               style={'color': '#7f8c8d', 'fontStyle': 'italic'}),
        dcc.Graph(id='anomaly-flags', style={'height': '100px'})
    ], style={'padding': '20px', 'margin': '20px', 'backgroundColor': '#ffffff', 'borderRadius': '10px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)'}),
    
    # Footer
    html.Div([
        html.P("Global Liquids Inventory Dashboard | Data updated weekly", 
               style={'textAlign': 'center', 'color': '#95a5a6', 'marginTop': '40px'})
    ])
], style={'fontFamily': 'Arial, sans-serif', 'backgroundColor': '#f8f9fa'})


# Callback to update column dropdown based on other selections
@app.callback(
    Output('column-dropdown', 'options'),
    Output('column-dropdown', 'value'),
    [Input('product-type-dropdown', 'value'),
     Input('geography-dropdown', 'value'),
     Input('storage-type-dropdown', 'value')]
)
def update_column_options(product_type, geography, storage_type):
    """Update available columns based on filter selections."""
    try:
        # Get filtered data to see available columns
        df = processor.get_filtered_data(product_type, geography, storage_type)
        numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
        
        options = [{'label': col, 'value': col} for col in numeric_cols]
        default_value = numeric_cols[0] if numeric_cols else None
        
        return options, default_value
    except Exception as e:
        return [], None


# Main callback to update all charts and statistics
@app.callback(
    [Output('main-chart', 'figure'),
     Output('percentile-trend', 'figure'),
     Output('anomaly-flags', 'figure'),
     Output('summary-stats', 'children')],
    [Input('product-type-dropdown', 'value'),
     Input('geography-dropdown', 'value'),
     Input('storage-type-dropdown', 'value'),
     Input('years-checklist', 'value'),
     Input('column-dropdown', 'value')]
)
def update_dashboard(product_type, geography, storage_type, selected_years, column):
    """Update all dashboard components based on user selections."""

    try:
        # Debug print to see what's being called
        print(f"🔄 Dashboard update called: product={product_type}, geo={geography}, storage={storage_type}, years={selected_years}, column={column}")

        # Prepare chart data
        chart_data = processor.prepare_chart_data(
            product_type=product_type,
            geography=geography,
            storage_type=storage_type,
            selected_years=selected_years,
            main_column=column
        )

        # Debug selected years data
        if 'selected_years_data' in chart_data:
            print(f"✅ Selected years data: {chart_data['selected_years_data'].shape}")
        else:
            print("❌ No selected years data found")

        # Create charts
        main_chart = create_main_chart(chart_data)
        percentile_trend = create_percentile_trend_chart(chart_data)
        anomaly_flags = create_anomaly_flags(chart_data)

    except Exception as e:
        # Create error charts if something goes wrong
        error_fig = go.Figure()
        error_fig.add_annotation(
            text=f"Error: {str(e)}",
            xref="paper", yref="paper",
            x=0.5, y=0.5, showarrow=False,
            font=dict(size=16, color="red")
        )
        main_chart = percentile_ribbon = anomaly_flags = error_fig

    # Generate summary statistics
    stats = get_summary_stats(chart_data)

    # Create summary statistics display
    if 'error' in stats:
        summary_div = html.Div([
            html.H4("⚠️ Error", style={'color': '#e74c3c'}),
            html.P(stats['error'])
        ])
    else:
        summary_div = html.Div([
            html.H3("📊 Summary Statistics", style={'color': '#34495e', 'marginBottom': '20px'}),

            html.Div([
                # Current Value Card
                html.Div([
                    html.H4("Current Value", style={'color': '#3498db', 'marginBottom': '10px'}),
                    html.H2(f"{stats.get('current_value', 'N/A'):,.0f}" if isinstance(stats.get('current_value'), (int, float)) else str(stats.get('current_value', 'N/A')),
                            style={'color': '#2c3e50', 'marginBottom': '5px'}),
                    html.P(f"Week: {stats.get('current_week', 'N/A')}", style={'color': '#7f8c8d'})
                ], style={'textAlign': 'center', 'padding': '20px', 'backgroundColor': '#ffffff',
                         'borderRadius': '10px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)',
                         'width': '22%', 'display': 'inline-block', 'margin': '1%'}),

                # Trend Card
                html.Div([
                    html.H4("Recent Trend", style={'color': '#27ae60', 'marginBottom': '10px'}),
                    html.H3(stats.get('recent_trend', 'N/A'), style={'color': '#2c3e50', 'marginBottom': '5px'}),
                    html.P(f"Change: {stats.get('trend_value', 0):,.0f}" if isinstance(stats.get('trend_value'), (int, float)) else "N/A",
                           style={'color': '#7f8c8d'})
                ], style={'textAlign': 'center', 'padding': '20px', 'backgroundColor': '#ffffff',
                         'borderRadius': '10px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)',
                         'width': '22%', 'display': 'inline-block', 'margin': '1%'}),

                # Data Range Card
                html.Div([
                    html.H4("Data Range", style={'color': '#9b59b6', 'marginBottom': '10px'}),
                    html.P(stats.get('data_range', 'N/A'), style={'color': '#2c3e50', 'fontSize': '14px', 'marginBottom': '5px'}),
                    html.P(f"Total weeks: {stats.get('total_weeks', 'N/A')}", style={'color': '#7f8c8d'})
                ], style={'textAlign': 'center', 'padding': '20px', 'backgroundColor': '#ffffff',
                         'borderRadius': '10px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)',
                         'width': '22%', 'display': 'inline-block', 'margin': '1%'}),

                # Anomalies Card
                html.Div([
                    html.H4("Anomalies", style={'color': '#e74c3c', 'marginBottom': '10px'}),
                    html.H3(f"{stats.get('total_anomalies', 0)}", style={'color': '#2c3e50', 'marginBottom': '5px'}),
                    html.P(f"High: {stats.get('high_anomalies', 0)} | Low: {stats.get('low_anomalies', 0)}",
                           style={'color': '#7f8c8d'})
                ], style={'textAlign': 'center', 'padding': '20px', 'backgroundColor': '#ffffff',
                         'borderRadius': '10px', 'boxShadow': '0 2px 4px rgba(0,0,0,0.1)',
                         'width': '22%', 'display': 'inline-block', 'margin': '1%'})
            ])
        ])

    return main_chart, percentile_trend, anomaly_flags, summary_div


# Load data when the app starts
if __name__ == '__main__':
    print("🚀 Starting Global Liquids Inventory Dashboard...")
    print("📊 Loading data...")
    processor.load_data()
    print("✅ Data loaded successfully!")
    print("🌐 Starting web server...")
    print("📱 Dashboard will be available at: http://localhost:8050")
    app.run(debug=True, host='0.0.0.0', port=8050)
