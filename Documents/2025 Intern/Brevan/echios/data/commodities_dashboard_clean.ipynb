{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 📊 Commodities Data Dashboard\n", "\n", "This notebook creates a comprehensive dashboard for commodities data analysis from Excel files.\n", "\n", "## 🎯 Features:\n", "- **Automatic Excel Import**: Imports all .xlsx files in the directory\n", "- **Smart Data Cleaning**: Handles empty rows, spaces, and inconsistent headers\n", "- **Time Series Processing**: Automatically detects and processes weekly/monthly time indices\n", "- **Interactive Visualizations**: Creates plots and charts for data exploration\n", "\n", "## 🚀 Usage:\n", "Run all cells sequentially to process your commodities data!"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import os\n", "import glob\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"Libraries imported successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def clean_excel_data(file_path, time_col_name='Week'):\n", "    \"\"\"\n", "    Clean Excel data by handling empty rows and finding proper headers.\n", "    \n", "    Parameters:\n", "    file_path (str): Path to the Excel file\n", "    time_col_name (str): Name for the time column (default: 'Week')\n", "    \n", "    Returns:\n", "    pd.DataFrame: Cleaned dataframe with proper headers and time index\n", "    \"\"\"\n", "    print(f\"Processing {file_path}...\")\n", "    \n", "    try:\n", "        # Read the raw data without headers\n", "        df_raw = pd.read_excel(file_path, header=None)\n", "        \n", "        # Find the first row that contains meaningful data (not all NaN)\n", "        header_row = None\n", "        data_start_row = None\n", "        \n", "        for i, row in df_raw.iterrows():\n", "            # Check if row has non-null values\n", "            non_null_count = row.notna().sum()\n", "            \n", "            if non_null_count > 1:  # More than 1 non-null value\n", "                # Check if this looks like a header row (contains text)\n", "                text_values = [val for val in row if pd.notna(val) and isinstance(val, str) and len(str(val).strip()) > 0]\n", "                if text_values and header_row is None:\n", "                    header_row = i\n", "                    print(f\"Found header row at index {i}\")\n", "                elif header_row is not None and data_start_row is None:\n", "                    # This looks like data (numbers)\n", "                    data_start_row = i\n", "                    print(f\"Found data start at index {i}\")\n", "                    break\n", "        \n", "        if header_row is None or data_start_row is None:\n", "            print(f\"Warning: Could not find proper structure in {file_path}\")\n", "            return pd.DataFrame()\n", "        \n", "        # Extract headers\n", "        headers = df_raw.iloc[header_row].fillna('').astype(str)\n", "        headers = [col.strip() if col.strip() else f'Column_{i}' for i, col in enumerate(headers)]\n", "        \n", "        # Set the first column as time column\n", "        headers[0] = time_col_name\n", "        \n", "        # Extract data\n", "        df_data = df_raw.iloc[data_start_row:].copy()\n", "        df_data.columns = headers[:len(df_data.columns)]\n", "        \n", "        # Reset index\n", "        df_data = df_data.reset_index(drop=True)\n", "        \n", "        # Clean the time column\n", "        df_data = df_data.dropna(subset=[time_col_name])\n", "        \n", "        # Try to convert time column to datetime\n", "        try:\n", "            df_data[time_col_name] = pd.to_datetime(df_data[time_col_name])\n", "        except:\n", "            print(f\"Warning: Could not convert time column to datetime in {file_path}\")\n", "        \n", "        # Convert numeric columns\n", "        for col in df_data.columns[1:]:\n", "            df_data[col] = pd.to_numeric(df_data[col], errors='coerce')\n", "        \n", "        print(f\"Cleaned data shape: {df_data.shape}\")\n", "        return df_data\n", "        \n", "    except Exception as e:\n", "        print(f\"Error processing {file_path}: {str(e)}\")\n", "        return pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Get all Excel files in the current directory\n", "excel_files = glob.glob('*.xlsx')\n", "excel_files = [f for f in excel_files if not f.startswith('~')]  # Exclude temporary files\n", "\n", "print(f\"Found {len(excel_files)} Excel files:\")\n", "for file in excel_files:\n", "    print(f\"  - {file}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load and clean all Excel files\n", "datasets = {}\n", "\n", "for file in excel_files:\n", "    # Determine if it's weekly or monthly data based on filename\n", "    time_col = 'Month' if 'monthly' in file.lower() else 'Week'\n", "    \n", "    df_clean = clean_excel_data(file, time_col_name=time_col)\n", "    \n", "    if not df_clean.empty:\n", "        # Use filename (without extension) as key\n", "        key = os.path.splitext(file)[0]\n", "        datasets[key] = df_clean\n", "        print(f\"Successfully loaded {key}: {df_clean.shape}\")\n", "    else:\n", "        print(f\"Failed to load {file}\")\n", "\n", "print(f\"\\nLoaded {len(datasets)} datasets successfully!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Display summary of loaded datasets\n", "print(\"Dataset Summary:\")\n", "print(\"=\" * 50)\n", "\n", "for name, df in datasets.items():\n", "    print(f\"\\n{name}:\")\n", "    print(f\"  Shape: {df.shape}\")\n", "    if not df.empty:\n", "        print(f\"  Time range: {df.iloc[:, 0].min()} to {df.iloc[:, 0].max()}\")\n", "        print(f\"  Columns: {list(df.columns[:5])}{'...' if len(df.columns) > 5 else ''}\")\n", "        print(f\"  Sample data:\")\n", "        print(df.head(3))\n", "        print()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Import plotting libraries (separate cell to avoid import issues)\n", "try:\n", "    import matplotlib.pyplot as plt\n", "    import seaborn as sns\n", "    \n", "    # Set basic plotting style\n", "    plt.style.use('default')\n", "    plt.rcParams['figure.figsize'] = (12, 6)\n", "    \n", "    print(\"Plotting libraries imported successfully!\")\n", "    PLOTTING_AVAILABLE = True\n", "except ImportError as e:\n", "    print(f\"Warning: Could not import plotting libraries: {e}\")\n", "    PLOTTING_AVAILABLE = False"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create basic visualizations\n", "def plot_dataset_overview(datasets):\n", "    \"\"\"\n", "    Create overview plots for all datasets\n", "    \"\"\"\n", "    if not PLOTTING_AVAILABLE:\n", "        print(\"Plotting not available. Skipping visualizations.\")\n", "        return\n", "    \n", "    if not datasets:\n", "        print(\"No datasets to plot.\")\n", "        return\n", "    \n", "    n_datasets = len(datasets)\n", "    fig, axes = plt.subplots(n_datasets, 1, figsize=(15, 5*n_datasets))\n", "    \n", "    if n_datasets == 1:\n", "        axes = [axes]\n", "    \n", "    for i, (name, df) in enumerate(datasets.items()):\n", "        if not df.empty:\n", "            time_col = df.columns[0]\n", "            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()\n", "            \n", "            # Plot first few numeric columns\n", "            for j, col in enumerate(numeric_cols[:3]):\n", "                if col in df.columns:\n", "                    axes[i].plot(df[time_col], df[col], label=col, linewidth=2)\n", "            \n", "            axes[i].set_title(f\"{name} - Key Metrics\", fontsize=12, fontweight='bold')\n", "            axes[i].set_xlabel(time_col)\n", "            axes[i].set_ylabel('Value')\n", "            axes[i].legend()\n", "            axes[i].grid(True, alpha=0.3)\n", "            axes[i].tick_params(axis='x', rotation=45)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# Create the overview plot\n", "plot_dataset_overview(datasets)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create summary dashboard\n", "def create_dashboard_summary(datasets):\n", "    \"\"\"\n", "    Create a comprehensive summary of all datasets\n", "    \"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"COMMODITIES DASHBOARD SUMMARY\")\n", "    print(\"=\"*60)\n", "    \n", "    for name, df in datasets.items():\n", "        if not df.empty:\n", "            print(f\"\\n📊 {name.upper()}:\")\n", "            print(f\"   📅 Time Period: {df.iloc[:, 0].min()} to {df.iloc[:, 0].max()}\")\n", "            print(f\"   📈 Data Points: {len(df)}\")\n", "            print(f\"   📋 Variables: {len(df.columns)-1}\")\n", "            \n", "            # Show latest values for key metrics\n", "            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()\n", "            if numeric_cols:\n", "                latest_row = df.iloc[-1]\n", "                print(f\"   🔢 Latest Values:\")\n", "                for col in numeric_cols[:3]:\n", "                    value = latest_row[col]\n", "                    if pd.notna(value):\n", "                        print(f\"      • {col}: {value:,.2f}\")\n", "            \n", "            # Basic statistics\n", "            if numeric_cols:\n", "                print(f\"   📊 Basic Statistics (first 3 columns):\")\n", "                stats = df[numeric_cols[:3]].describe()\n", "                print(stats.round(2))\n", "\n", "# Run the dashboard summary\n", "create_dashboard_summary(datasets)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Export functionality\n", "def export_cleaned_data(datasets, export_dir='cleaned_data'):\n", "    \"\"\"\n", "    Export all cleaned datasets to CSV files\n", "    \"\"\"\n", "    if not os.path.exists(export_dir):\n", "        os.makedirs(export_dir)\n", "        print(f\"Created directory: {export_dir}\")\n", "    \n", "    for name, df in datasets.items():\n", "        if not df.empty:\n", "            filename = f\"{export_dir}/{name}_cleaned.csv\"\n", "            df.to_csv(filename, index=False)\n", "            print(f\"Exported {name} to {filename}\")\n", "    \n", "    print(f\"\\nAll cleaned data exported to '{export_dir}' directory!\")\n", "\n", "# Uncomment the line below to export data\n", "# export_cleaned_data(datasets)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 2}