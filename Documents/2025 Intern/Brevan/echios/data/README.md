# 🛢️ Global Liquids Inventory Tracking Dashboard

A dynamic UI dashboard for global liquids inventory data tracking with advanced analytics and visualization capabilities.

## 🎯 Purpose

Instantly see where today's stocks sit versus their historic seasonal range with comprehensive drill-down capabilities and anomaly detection.

## ✨ Key Features

### 1. 📊 Three-Level Drill-Down Options
- **Product Type**: Toggle between crude oil, total products, gasoline, distillates, core products, and all
- **Geography**: Filter by region (global, U.S., ARA, Singapore, and all)
- **Storage Type**: Land storage, floating storage, oil on water, and all

### 2. 📈 5-Year Range Shading
- Weekly inventory levels plotted against 5-year min/max band
- Professional light blue shading for the historical range
- Dashed line showing 5-year average
- Customizable year selection with checkbox list

### 3. 🌈 Week-of-Year Percentile Ribbon
- Color-coded ribbon showing percentile ranking of each weekly stock point
- Green (low percentile) to red (high percentile) color scale
- Based on 5-year distribution for each week of the year

### 4. 🚩 Anomaly Flagging
- Automatic highlighting when current week's level is in top/bottom 10%
- Visual flags placed underneath the percentile ribbon
- Separate indicators for high and low anomalies

## 📁 File Structure

```
inventory_dashboard_functions.py  # Core data processing functions
inventory_dashboard.py           # Main Dash application
demo_dashboard.py               # Demo script (no web dependencies)
test_dashboard.py              # Basic functionality tests
data/                          # Data directory
├── merge_total.xlsx          # Combined land + water data
├── merge_land.xlsx           # Land storage data by region
├── merge_water.xlsx          # Oil on water data
└── merge_floating.xlsx       # Floating storage data
```

## 🚀 Quick Start

### Option 1: Run Demo (No Installation Required)
```bash
python3 demo_dashboard.py
```
This runs a command-line demo showing all the analytics without requiring web dependencies.

### Option 2: Full Interactive Dashboard
1. **Install Dependencies**:
   ```bash
   pip install dash plotly pandas numpy openpyxl
   ```

2. **Run Dashboard**:
   ```bash
   python3 inventory_dashboard.py
   ```

3. **Open Browser**:
   Navigate to `http://localhost:8050`

## 📊 Data Structure

The dashboard works with four main datasets:

- **Total (merge_total.xlsx)**: Combined land + water inventory data (15 columns, 440+ weeks)
- **Land (merge_land.xlsx)**: Land-based storage by region (21 columns)
- **Water (merge_water.xlsx)**: Oil on water data (7 columns)  
- **Floating (merge_floating.xlsx)**: Floating storage data (7 columns)

### Key Data Columns:
- `Week`: Weekly time index (2017-2025)
- `Total Land + Water`: Combined inventory levels
- `Total Crude Land + Water`: Crude oil inventories
- `Total Products Land + Water`: Product inventories
- Regional breakdowns (US, ARA, Singapore)
- Product-specific data (gasoline, distillates, etc.)

## 🔧 Core Functions

### InventoryDataProcessor Class
- `load_data()`: Load all Excel files
- `get_filtered_data()`: Apply drill-down filters
- `calculate_5year_stats()`: Generate historical statistics
- `calculate_percentiles()`: Compute percentile rankings
- `detect_anomalies()`: Flag unusual values
- `prepare_chart_data()`: Package data for visualization

### Visualization Functions
- `create_main_chart()`: 5-year range chart with current data
- `create_percentile_ribbon()`: Color-coded percentile visualization
- `create_anomaly_flags()`: Anomaly indicator chart
- `get_summary_stats()`: Dashboard summary statistics

## 📈 Analytics Features

### 5-Year Statistical Analysis
- Min/max bands for each week of the year
- Rolling averages and standard deviations
- Customizable year selection for analysis period

### Percentile Calculations
- Week-of-year grouping for seasonal analysis
- Percentile ranking within historical distribution
- Color mapping for intuitive visualization

### Anomaly Detection
- Configurable threshold (default: top/bottom 10%)
- Separate flagging for high and low anomalies
- Historical context for current readings

## 🎨 Dashboard Layout

1. **Header**: Title and description
2. **Control Panel**: All drill-down options and filters
3. **Summary Statistics**: Key metrics and current status
4. **Main Chart**: 5-year range visualization with current data
5. **Percentile Ribbon**: Week-of-year color-coded analysis
6. **Anomaly Flags**: Visual indicators for unusual readings

## 🔍 Usage Examples

### Basic Analysis
```python
from inventory_dashboard_functions import InventoryDataProcessor

# Initialize and load data
processor = InventoryDataProcessor()
processor.load_data()

# Get filtered data
df = processor.get_filtered_data('crude_oil', 'global', 'total')

# Prepare complete analysis
chart_data = processor.prepare_chart_data('crude_oil', 'global', 'total', [2023, 2024])
```

### Custom Analysis
```python
# Focus on specific region and product
chart_data = processor.prepare_chart_data(
    product_type='gasoline',
    geography='us', 
    storage_type='land',
    selected_years=[2020, 2021, 2022, 2023, 2024]
)
```

## 🛠️ Customization

### Adding New Data Sources
1. Place Excel files in the `data/` directory
2. Update `storage_mapping` in `InventoryDataProcessor`
3. Add new options to dashboard dropdowns

### Modifying Filters
- Edit `product_mapping` for new product categories
- Update `geography_mapping` for additional regions
- Adjust `storage_mapping` for new storage types

### Changing Anomaly Thresholds
```python
# Detect top/bottom 5% instead of 10%
anomalies = processor.detect_anomalies(df, threshold=0.05)
```

## 📋 Requirements

- Python 3.7+
- pandas
- numpy
- openpyxl (for Excel file reading)
- dash (for web interface)
- plotly (for interactive charts)

## 🤝 Contributing

1. Test changes with `demo_dashboard.py`
2. Ensure all data processing functions work independently
3. Update documentation for new features
4. Test dashboard responsiveness across different data selections

## 📞 Support

For questions or issues:
1. Run `demo_dashboard.py` to verify data processing
2. Check data file formats and column names
3. Ensure all required packages are installed
4. Verify data directory structure matches expectations
