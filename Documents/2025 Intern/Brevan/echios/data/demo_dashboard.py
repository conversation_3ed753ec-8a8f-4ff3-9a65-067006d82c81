"""
Demo script for Global Liquids Inventory Dashboard Functions

This script demonstrates the core functionality without requiring Dash/Plotly.
It shows how the data processing and analysis functions work.
"""

import pandas as pd
import numpy as np
from datetime import datetime
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

class SimpleInventoryProcessor:
    """Simplified version of the inventory data processor for demo purposes."""
    
    def __init__(self, data_path="data/"):
        self.data_path = data_path
        self.data = {}
        
    def load_data(self):
        """Load all data files."""
        print("📊 Loading inventory data...")
        
        files = {
            'total': 'merge_total.xlsx',
            'land': 'merge_land.xlsx', 
            'water': 'merge_water.xlsx',
            'floating': 'merge_floating.xlsx'
        }
        
        for storage_type, filename in files.items():
            try:
                filepath = f"{self.data_path}{filename}"
                df = pd.read_excel(filepath)
                df['Week'] = pd.to_datetime(df['Week'])
                df = df.sort_values('Week').reset_index(drop=True)
                self.data[storage_type] = df
                print(f"✅ Loaded {storage_type}: {df.shape}")
            except Exception as e:
                print(f"❌ Error loading {filename}: {e}")
        
        print(f"✅ Data loading complete!")
    
    def calculate_5year_stats(self, df, column, selected_years=None):
        """Calculate 5-year statistics."""
        if column not in df.columns:
            return pd.DataFrame()
        
        df_work = df.copy()
        df_work['week_of_year'] = df_work['Week'].dt.isocalendar().week
        df_work['year'] = df_work['Week'].dt.year
        
        if selected_years:
            df_work = df_work[df_work['year'].isin(selected_years)]
        
        stats = df_work.groupby('week_of_year')[column].agg([
            'min', 'max', 'mean', 'std', 'count'
        ]).reset_index()
        
        stats.columns = ['week_of_year', 'min_5yr', 'max_5yr', 'avg_5yr', 'std_5yr', 'count']
        return stats
    
    def calculate_percentiles(self, df, column):
        """Calculate percentiles for each data point."""
        if column not in df.columns:
            return pd.DataFrame()
        
        df_work = df.copy()
        df_work['week_of_year'] = df_work['Week'].dt.isocalendar().week
        
        def calc_percentile(group):
            if len(group) > 0:
                group['percentile'] = group[column].rank(pct=True) * 100
            return group
        
        result = df_work.groupby('week_of_year').apply(calc_percentile)
        return result.reset_index(drop=True)
    
    def detect_anomalies(self, df_with_percentiles, threshold=0.1):
        """Detect anomalies based on percentiles."""
        df_result = df_with_percentiles.copy()
        df_result['is_anomaly_high'] = df_result['percentile'] >= (100 - threshold * 100)
        df_result['is_anomaly_low'] = df_result['percentile'] <= (threshold * 100)
        df_result['is_anomaly'] = df_result['is_anomaly_high'] | df_result['is_anomaly_low']
        return df_result
    
    def demo_analysis(self, storage_type='total', column=None):
        """Run a complete demo analysis."""
        print(f"\n🔍 Running demo analysis for {storage_type} storage...")
        
        if storage_type not in self.data:
            print(f"❌ No data available for {storage_type}")
            return
        
        df = self.data[storage_type]
        
        # Auto-select column if not provided
        if column is None:
            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            column = numeric_cols[0] if numeric_cols else None
        
        if column is None or column not in df.columns:
            print(f"❌ Column {column} not found")
            return
        
        print(f"📈 Analyzing column: {column}")
        
        # Calculate statistics
        stats_5yr = self.calculate_5year_stats(df, column, [2020, 2021, 2022, 2023, 2024])
        df_with_percentiles = self.calculate_percentiles(df, column)
        df_with_anomalies = self.detect_anomalies(df_with_percentiles)
        
        # Print results
        print(f"\n📊 Results Summary:")
        print(f"   • Total data points: {len(df)}")
        print(f"   • Date range: {df['Week'].min().strftime('%Y-%m-%d')} to {df['Week'].max().strftime('%Y-%m-%d')}")
        print(f"   • 5-year stats calculated for {len(stats_5yr)} weeks")
        print(f"   • Current value: {df[column].iloc[-1]:,.0f}")
        print(f"   • Latest week: {df['Week'].iloc[-1].strftime('%Y-%m-%d')}")
        
        # Anomaly summary
        total_anomalies = df_with_anomalies['is_anomaly'].sum()
        high_anomalies = df_with_anomalies['is_anomaly_high'].sum()
        low_anomalies = df_with_anomalies['is_anomaly_low'].sum()
        
        print(f"\n🚩 Anomaly Detection:")
        print(f"   • Total anomalies: {total_anomalies}")
        print(f"   • High anomalies (top 10%): {high_anomalies}")
        print(f"   • Low anomalies (bottom 10%): {low_anomalies}")
        
        # Recent trend
        recent_data = df.tail(4)
        if len(recent_data) >= 2:
            trend = recent_data[column].iloc[-1] - recent_data[column].iloc[0]
            trend_direction = 'Increasing' if trend > 0 else 'Decreasing' if trend < 0 else 'Stable'
            print(f"\n📈 Recent Trend (last 4 weeks): {trend_direction}")
            print(f"   • Change: {trend:,.0f}")
        
        # Show some sample statistics
        if not stats_5yr.empty:
            print(f"\n📋 Sample 5-Year Statistics (first 5 weeks):")
            print(stats_5yr.head().to_string(index=False))
        
        # Show recent anomalies
        recent_anomalies = df_with_anomalies[df_with_anomalies['is_anomaly']].tail(5)
        if not recent_anomalies.empty:
            print(f"\n🔍 Recent Anomalies:")
            for _, row in recent_anomalies.iterrows():
                anomaly_type = "High" if row['is_anomaly_high'] else "Low"
                print(f"   • {row['Week'].strftime('%Y-%m-%d')}: {anomaly_type} ({row['percentile']:.1f}th percentile)")
        
        return {
            'data': df,
            'column': column,
            'stats_5yr': stats_5yr,
            'percentile_data': df_with_percentiles,
            'anomaly_data': df_with_anomalies
        }


def main():
    """Run the demo."""
    print("Global Liquids Inventory Dashboard Demo")
    print("=" * 50)
    
    # Initialize processor
    processor = SimpleInventoryProcessor()
    
    # Load data
    processor.load_data()
    
    # Run demo analysis for different storage types
    storage_types = ['total', 'land', 'water', 'floating']
    
    for storage_type in storage_types:
        if storage_type in processor.data:
            result = processor.demo_analysis(storage_type)
            print("\n" + "="*50)
    
    print("\n✅ Demo completed successfully!")
    print("\n💡 Next steps:")
    print("   1. Install required packages: pip install dash plotly")
    print("   2. Run the full dashboard: python inventory_dashboard.py")
    print("   3. Open browser to http://localhost:8050")


if __name__ == "__main__":
    main()
