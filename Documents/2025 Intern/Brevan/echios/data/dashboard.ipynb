{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["merge 5yr HiLo.xlsx\n", ".DS_Store\n", "merge.xlsx\n", "cumulative.xlsx\n", "dashboard.ipynb\n", "monthly.xlsx\n", "merge 5yr HiLo (1).xlsx\n", "summary.xlsx\n"]}], "source": ["import os\n", "\n", "#get the current working directory\n", "os.getcwd()\n", "\n", "#list all files in the current directory\n", "files = os.listdir()\n", "\n", "for f in files:\n", "    print(f)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import openpyxl\n", "df_merge = pd.read_excel('merge.xlsx')"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Week</th>\n", "      <th>Total Land + Water</th>\n", "      <th>Total Crude Land + Water</th>\n", "      <th>Total Products Land + Water</th>\n", "      <th>Total Crude / Core Products Land + Water</th>\n", "      <th>Total Global Commercial Liquids on Land</th>\n", "      <th>Total Crude / Core Products on Land</th>\n", "      <th>Total Global Commercial Crude Oil</th>\n", "      <th>Total Global Products Land</th>\n", "      <th>Total Global Core Products Land + Water</th>\n", "      <th>...</th>\n", "      <th>Total Crude Land + Water.1</th>\n", "      <th>Total Products Land + Water.1</th>\n", "      <th>Total Global Commercial Liquids on Land.1</th>\n", "      <th>Total Global Commercial Crude Oil.1</th>\n", "      <th>Total Global Products Land.1</th>\n", "      <th>Total Global Core Products Land + Water.1</th>\n", "      <th>Total Global Gasoline Land + Water.1</th>\n", "      <th>Total Global Distillate Land + Water.1</th>\n", "      <th>Total Global Core Products Land.1</th>\n", "      <th>Total Global Commercial Crude Oil + Core Products Land</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2017-01-06</td>\n", "      <td>6.121479e+06</td>\n", "      <td>5.161332e+06</td>\n", "      <td>9.601464e+05</td>\n", "      <td>5.762742e+06</td>\n", "      <td>4919051.441</td>\n", "      <td>4564548.941</td>\n", "      <td>3988219.641</td>\n", "      <td>930831.80</td>\n", "      <td>601409.964286</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2017-01-13</td>\n", "      <td>6.188136e+06</td>\n", "      <td>5.156263e+06</td>\n", "      <td>1.031873e+06</td>\n", "      <td>5.825710e+06</td>\n", "      <td>4923051.796</td>\n", "      <td>4576455.496</td>\n", "      <td>3992746.746</td>\n", "      <td>930305.05</td>\n", "      <td>669447.146429</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2017-01-20</td>\n", "      <td>6.256074e+06</td>\n", "      <td>5.139623e+06</td>\n", "      <td>1.116451e+06</td>\n", "      <td>5.883212e+06</td>\n", "      <td>4928921.230</td>\n", "      <td>4585184.430</td>\n", "      <td>3986745.380</td>\n", "      <td>942175.85</td>\n", "      <td>743589.086429</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2017-01-27</td>\n", "      <td>6.331286e+06</td>\n", "      <td>5.167375e+06</td>\n", "      <td>1.163911e+06</td>\n", "      <td>5.952881e+06</td>\n", "      <td>4953389.508</td>\n", "      <td>4614433.508</td>\n", "      <td>4009732.058</td>\n", "      <td>943657.45</td>\n", "      <td>785505.622857</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2017-02-03</td>\n", "      <td>6.354753e+06</td>\n", "      <td>5.171557e+06</td>\n", "      <td>1.183196e+06</td>\n", "      <td>5.977780e+06</td>\n", "      <td>4966688.948</td>\n", "      <td>4636838.848</td>\n", "      <td>4034494.648</td>\n", "      <td>932194.30</td>\n", "      <td>806222.936429</td>\n", "      <td>...</td>\n", "      <td>365.186117</td>\n", "      <td>7966.061556</td>\n", "      <td>1701.339536</td>\n", "      <td>1652.678821</td>\n", "      <td>48.660714</td>\n", "      <td>7314.749005</td>\n", "      <td>2368.447321</td>\n", "      <td>2097.133087</td>\n", "      <td>929.103571</td>\n", "      <td>2581.782393</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 63 columns</p>\n", "</div>"], "text/plain": ["        Week  Total Land + Water  Total Crude Land + Water  \\\n", "0 2017-01-06        6.121479e+06              5.161332e+06   \n", "1 2017-01-13        6.188136e+06              5.156263e+06   \n", "2 2017-01-20        6.256074e+06              5.139623e+06   \n", "3 2017-01-27        6.331286e+06              5.167375e+06   \n", "4 2017-02-03        6.354753e+06              5.171557e+06   \n", "\n", "   Total Products Land + Water  Total Crude / Core Products Land + Water  \\\n", "0                 9.601464e+05                              5.762742e+06   \n", "1                 1.031873e+06                              5.825710e+06   \n", "2                 1.116451e+06                              5.883212e+06   \n", "3                 1.163911e+06                              5.952881e+06   \n", "4                 1.183196e+06                              5.977780e+06   \n", "\n", "   Total Global Commercial Liquids on Land  \\\n", "0                              4919051.441   \n", "1                              4923051.796   \n", "2                              4928921.230   \n", "3                              4953389.508   \n", "4                              4966688.948   \n", "\n", "   Total Crude / Core Products on Land  Total Global Commercial Crude Oil  \\\n", "0                          4564548.941                        3988219.641   \n", "1                          4576455.496                        3992746.746   \n", "2                          4585184.430                        3986745.380   \n", "3                          4614433.508                        4009732.058   \n", "4                          4636838.848                        4034494.648   \n", "\n", "   Total Global Products Land  Total Global Core Products Land + Water  ...  \\\n", "0                   930831.80                            601409.964286  ...   \n", "1                   930305.05                            669447.146429  ...   \n", "2                   942175.85                            743589.086429  ...   \n", "3                   943657.45                            785505.622857  ...   \n", "4                   932194.30                            806222.936429  ...   \n", "\n", "   Total Crude Land + Water.1  Total Products Land + Water.1  \\\n", "0                         NaN                            NaN   \n", "1                         NaN                            NaN   \n", "2                         NaN                            NaN   \n", "3                         NaN                            NaN   \n", "4                  365.186117                    7966.061556   \n", "\n", "   Total Global Commercial Liquids on Land.1  \\\n", "0                                        NaN   \n", "1                                        NaN   \n", "2                                        NaN   \n", "3                                        NaN   \n", "4                                1701.339536   \n", "\n", "   Total Global Commercial Crude Oil.1  Total Global Products Land.1  \\\n", "0                                  NaN                           NaN   \n", "1                                  NaN                           NaN   \n", "2                                  NaN                           NaN   \n", "3                                  NaN                           NaN   \n", "4                          1652.678821                     48.660714   \n", "\n", "   Total Global Core Products Land + Water.1  \\\n", "0                                        NaN   \n", "1                                        NaN   \n", "2                                        NaN   \n", "3                                        NaN   \n", "4                                7314.749005   \n", "\n", "   Total Global Gasoline Land + Water.1  \\\n", "0                                   NaN   \n", "1                                   NaN   \n", "2                                   NaN   \n", "3                                   NaN   \n", "4                           2368.447321   \n", "\n", "   Total Global Distillate Land + Water.1  Total Global Core Products Land.1  \\\n", "0                                     NaN                                NaN   \n", "1                                     NaN                                NaN   \n", "2                                     NaN                                NaN   \n", "3                                     NaN                                NaN   \n", "4                             2097.133087                         929.103571   \n", "\n", "   Total Global Commercial Crude Oil + Core Products Land  \n", "0                                                NaN       \n", "1                                                NaN       \n", "2                                                NaN       \n", "3                                                NaN       \n", "4                                        2581.782393       \n", "\n", "[5 rows x 63 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df_merge.head()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def read_excel_with_dynamic_header(file_path, sheet_name=0):\n", "    \"\"\"\n", "    Read Excel file with automatic header detection.\n", "    Handles empty rows at the beginning and multi-level headers.\n", "    \"\"\"\n", "    # First read without header to inspect structure\n", "    temp_df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)\n", "    \n", "    # Find first non-empty row\n", "    first_data_row = 0\n", "    while temp_df.iloc[first_data_row].isna().all() and first_data_row < len(temp_df):\n", "        first_data_row += 1\n", "    \n", "    # Check if next row might be part of a multi-level header\n", "    if first_data_row + 1 < len(temp_df):\n", "        next_row_empty_count = temp_df.iloc[first_data_row + 1].isna().sum()\n", "        if next_row_empty_count < len(temp_df.columns) * 0.5:  # Less than half empty cells\n", "            # Multi-level header detected\n", "            df = pd.read_excel(file_path, sheet_name=sheet_name, header=[first_data_row, first_data_row + 1])\n", "            print(\"Multi-level header detected\")\n", "        else:\n", "            # Single level header\n", "            df = pd.read_excel(file_path, sheet_name=sheet_name, header=first_data_row)\n", "            print(\"Single-level header detected\")\n", "    else:\n", "        # Single level header\n", "        df = pd.read_excel(file_path, sheet_name=sheet_name, header=first_data_row)\n", "        print(\"Single-level header detected\")\n", "    \n", "    return df\n", "\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def process_excel_file(file_path, sheet_name=0):\n", "    \"\"\"\n", "    Complete workflow: read file, inspect data, visualize.\n", "    \"\"\"\n", "    # Read file with dynamic header detection\n", "    df = read_excel_with_dynamic_header(file_path, sheet_name)\n", "    \n", "    "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["import openpyxl\n", "df_merge = pd.read_excel('merge.xlsx')"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["df_merge = df_merge.set_index('Week')"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Total Land + Water</th>\n", "      <th>Total Crude Land + Water</th>\n", "      <th>Total Products Land + Water</th>\n", "      <th>Total Crude / Core Products Land + Water</th>\n", "      <th>Total Global Commercial Liquids on Land</th>\n", "      <th>Total Crude / Core Products on Land</th>\n", "      <th>Total Global Commercial Crude Oil</th>\n", "      <th>Total Global Products Land</th>\n", "      <th>Total Global Core Products Land + Water</th>\n", "      <th>Total Global Gasoline Land + Water</th>\n", "      <th>...</th>\n", "      <th>Total Crude Land + Water.1</th>\n", "      <th>Total Products Land + Water.1</th>\n", "      <th>Total Global Commercial Liquids on Land.1</th>\n", "      <th>Total Global Commercial Crude Oil.1</th>\n", "      <th>Total Global Products Land.1</th>\n", "      <th>Total Global Core Products Land + Water.1</th>\n", "      <th>Total Global Gasoline Land + Water.1</th>\n", "      <th>Total Global Distillate Land + Water.1</th>\n", "      <th>Total Global Core Products Land.1</th>\n", "      <th>Total Global Commercial Crude Oil + Core Products Land</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Week</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2017-01-06</th>\n", "      <td>6.121479e+06</td>\n", "      <td>5.161332e+06</td>\n", "      <td>9.601464e+05</td>\n", "      <td>5.762742e+06</td>\n", "      <td>4919051.441</td>\n", "      <td>4564548.941</td>\n", "      <td>3988219.641</td>\n", "      <td>930831.80</td>\n", "      <td>601409.964286</td>\n", "      <td>267300.373571</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2017-01-13</th>\n", "      <td>6.188136e+06</td>\n", "      <td>5.156263e+06</td>\n", "      <td>1.031873e+06</td>\n", "      <td>5.825710e+06</td>\n", "      <td>4923051.796</td>\n", "      <td>4576455.496</td>\n", "      <td>3992746.746</td>\n", "      <td>930305.05</td>\n", "      <td>669447.146429</td>\n", "      <td>288839.907143</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2017-01-20</th>\n", "      <td>6.256074e+06</td>\n", "      <td>5.139623e+06</td>\n", "      <td>1.116451e+06</td>\n", "      <td>5.883212e+06</td>\n", "      <td>4928921.230</td>\n", "      <td>4585184.430</td>\n", "      <td>3986745.380</td>\n", "      <td>942175.85</td>\n", "      <td>743589.086429</td>\n", "      <td>315698.407143</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2017-01-27</th>\n", "      <td>6.331286e+06</td>\n", "      <td>5.167375e+06</td>\n", "      <td>1.163911e+06</td>\n", "      <td>5.952881e+06</td>\n", "      <td>4953389.508</td>\n", "      <td>4614433.508</td>\n", "      <td>4009732.058</td>\n", "      <td>943657.45</td>\n", "      <td>785505.622857</td>\n", "      <td>330481.266429</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2017-02-03</th>\n", "      <td>6.354753e+06</td>\n", "      <td>5.171557e+06</td>\n", "      <td>1.183196e+06</td>\n", "      <td>5.977780e+06</td>\n", "      <td>4966688.948</td>\n", "      <td>4636838.848</td>\n", "      <td>4034494.648</td>\n", "      <td>932194.30</td>\n", "      <td>806222.936429</td>\n", "      <td>333616.898571</td>\n", "      <td>...</td>\n", "      <td>365.186117</td>\n", "      <td>7966.061556</td>\n", "      <td>1701.339536</td>\n", "      <td>1652.678821</td>\n", "      <td>48.660714</td>\n", "      <td>7314.749005</td>\n", "      <td>2368.447321</td>\n", "      <td>2097.133087</td>\n", "      <td>929.103571</td>\n", "      <td>2581.782393</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-05-09</th>\n", "      <td>6.311178e+06</td>\n", "      <td>5.029070e+06</td>\n", "      <td>1.282109e+06</td>\n", "      <td>5.820714e+06</td>\n", "      <td>4737987.422</td>\n", "      <td>4350261.022</td>\n", "      <td>3873301.822</td>\n", "      <td>864685.60</td>\n", "      <td>791644.752381</td>\n", "      <td>334095.405238</td>\n", "      <td>...</td>\n", "      <td>2600.023447</td>\n", "      <td>120.800187</td>\n", "      <td>2753.069893</td>\n", "      <td>2563.762750</td>\n", "      <td>189.307143</td>\n", "      <td>-614.870527</td>\n", "      <td>-382.404864</td>\n", "      <td>-428.782976</td>\n", "      <td>-691.121429</td>\n", "      <td>1872.641321</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-05-16</th>\n", "      <td>6.320712e+06</td>\n", "      <td>5.040090e+06</td>\n", "      <td>1.280622e+06</td>\n", "      <td>5.829608e+06</td>\n", "      <td>4757103.628</td>\n", "      <td>4367245.028</td>\n", "      <td>3889747.878</td>\n", "      <td>867355.75</td>\n", "      <td>789518.385714</td>\n", "      <td>332477.492381</td>\n", "      <td>...</td>\n", "      <td>1688.424964</td>\n", "      <td>53.511871</td>\n", "      <td>3071.906393</td>\n", "      <td>2745.399250</td>\n", "      <td>326.507143</td>\n", "      <td>-479.884133</td>\n", "      <td>-203.277092</td>\n", "      <td>-213.833486</td>\n", "      <td>-398.328571</td>\n", "      <td>2347.070679</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-05-23</th>\n", "      <td>6.297677e+06</td>\n", "      <td>5.014539e+06</td>\n", "      <td>1.283138e+06</td>\n", "      <td>5.803764e+06</td>\n", "      <td>4747218.662</td>\n", "      <td>4353120.062</td>\n", "      <td>3875806.912</td>\n", "      <td>871411.75</td>\n", "      <td>789225.111905</td>\n", "      <td>328498.724762</td>\n", "      <td>...</td>\n", "      <td>1032.080396</td>\n", "      <td>-192.218146</td>\n", "      <td>2572.486536</td>\n", "      <td>2390.897250</td>\n", "      <td>181.589286</td>\n", "      <td>-544.774796</td>\n", "      <td>-310.257160</td>\n", "      <td>-150.142942</td>\n", "      <td>-391.835714</td>\n", "      <td>1999.061536</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-05-30</th>\n", "      <td>6.350730e+06</td>\n", "      <td>5.053297e+06</td>\n", "      <td>1.297433e+06</td>\n", "      <td>5.850345e+06</td>\n", "      <td>4778353.048</td>\n", "      <td>4377260.548</td>\n", "      <td>3890786.698</td>\n", "      <td>887566.35</td>\n", "      <td>797047.320952</td>\n", "      <td>330760.840952</td>\n", "      <td>...</td>\n", "      <td>1739.923029</td>\n", "      <td>494.729864</td>\n", "      <td>2919.415750</td>\n", "      <td>2091.372893</td>\n", "      <td>828.042857</td>\n", "      <td>-29.885323</td>\n", "      <td>-183.703418</td>\n", "      <td>46.202602</td>\n", "      <td>130.207143</td>\n", "      <td>2221.580036</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2025-06-06</th>\n", "      <td>6.349549e+06</td>\n", "      <td>5.041248e+06</td>\n", "      <td>1.308301e+06</td>\n", "      <td>5.840617e+06</td>\n", "      <td>4779692.491</td>\n", "      <td>4370939.091</td>\n", "      <td>3883335.791</td>\n", "      <td>896356.70</td>\n", "      <td>799368.795238</td>\n", "      <td>331316.581429</td>\n", "      <td>...</td>\n", "      <td>434.945509</td>\n", "      <td>935.427466</td>\n", "      <td>1489.466750</td>\n", "      <td>358.356036</td>\n", "      <td>1131.110714</td>\n", "      <td>275.858673</td>\n", "      <td>-99.243707</td>\n", "      <td>138.046786</td>\n", "      <td>380.146429</td>\n", "      <td>738.502464</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>440 rows × 62 columns</p>\n", "</div>"], "text/plain": ["            Total Land + Water  Total Crude Land + Water  \\\n", "Week                                                       \n", "2017-01-06        6.121479e+06              5.161332e+06   \n", "2017-01-13        6.188136e+06              5.156263e+06   \n", "2017-01-20        6.256074e+06              5.139623e+06   \n", "2017-01-27        6.331286e+06              5.167375e+06   \n", "2017-02-03        6.354753e+06              5.171557e+06   \n", "...                        ...                       ...   \n", "2025-05-09        6.311178e+06              5.029070e+06   \n", "2025-05-16        6.320712e+06              5.040090e+06   \n", "2025-05-23        6.297677e+06              5.014539e+06   \n", "2025-05-30        6.350730e+06              5.053297e+06   \n", "2025-06-06        6.349549e+06              5.041248e+06   \n", "\n", "            Total Products Land + Water  \\\n", "Week                                      \n", "2017-01-06                 9.601464e+05   \n", "2017-01-13                 1.031873e+06   \n", "2017-01-20                 1.116451e+06   \n", "2017-01-27                 1.163911e+06   \n", "2017-02-03                 1.183196e+06   \n", "...                                 ...   \n", "2025-05-09                 1.282109e+06   \n", "2025-05-16                 1.280622e+06   \n", "2025-05-23                 1.283138e+06   \n", "2025-05-30                 1.297433e+06   \n", "2025-06-06                 1.308301e+06   \n", "\n", "            Total Crude / Core Products Land + Water  \\\n", "Week                                                   \n", "2017-01-06                              5.762742e+06   \n", "2017-01-13                              5.825710e+06   \n", "2017-01-20                              5.883212e+06   \n", "2017-01-27                              5.952881e+06   \n", "2017-02-03                              5.977780e+06   \n", "...                                              ...   \n", "2025-05-09                              5.820714e+06   \n", "2025-05-16                              5.829608e+06   \n", "2025-05-23                              5.803764e+06   \n", "2025-05-30                              5.850345e+06   \n", "2025-06-06                              5.840617e+06   \n", "\n", "            Total Global Commercial Liquids on Land  \\\n", "Week                                                  \n", "2017-01-06                              4919051.441   \n", "2017-01-13                              4923051.796   \n", "2017-01-20                              4928921.230   \n", "2017-01-27                              4953389.508   \n", "2017-02-03                              4966688.948   \n", "...                                             ...   \n", "2025-05-09                              4737987.422   \n", "2025-05-16                              4757103.628   \n", "2025-05-23                              4747218.662   \n", "2025-05-30                              4778353.048   \n", "2025-06-06                              4779692.491   \n", "\n", "            Total Crude / Core Products on Land  \\\n", "Week                                              \n", "2017-01-06                          4564548.941   \n", "2017-01-13                          4576455.496   \n", "2017-01-20                          4585184.430   \n", "2017-01-27                          4614433.508   \n", "2017-02-03                          4636838.848   \n", "...                                         ...   \n", "2025-05-09                          4350261.022   \n", "2025-05-16                          4367245.028   \n", "2025-05-23                          4353120.062   \n", "2025-05-30                          4377260.548   \n", "2025-06-06                          4370939.091   \n", "\n", "            Total Global Commercial Crude Oil  Total Global Products Land  \\\n", "Week                                                                        \n", "2017-01-06                        3988219.641                   930831.80   \n", "2017-01-13                        3992746.746                   930305.05   \n", "2017-01-20                        3986745.380                   942175.85   \n", "2017-01-27                        4009732.058                   943657.45   \n", "2017-02-03                        4034494.648                   932194.30   \n", "...                                       ...                         ...   \n", "2025-05-09                        3873301.822                   864685.60   \n", "2025-05-16                        3889747.878                   867355.75   \n", "2025-05-23                        3875806.912                   871411.75   \n", "2025-05-30                        3890786.698                   887566.35   \n", "2025-06-06                        3883335.791                   896356.70   \n", "\n", "            Total Global Core Products Land + Water  \\\n", "Week                                                  \n", "2017-01-06                            601409.964286   \n", "2017-01-13                            669447.146429   \n", "2017-01-20                            743589.086429   \n", "2017-01-27                            785505.622857   \n", "2017-02-03                            806222.936429   \n", "...                                             ...   \n", "2025-05-09                            791644.752381   \n", "2025-05-16                            789518.385714   \n", "2025-05-23                            789225.111905   \n", "2025-05-30                            797047.320952   \n", "2025-06-06                            799368.795238   \n", "\n", "            Total Global Gasoline Land + Water  ...  \\\n", "Week                                            ...   \n", "2017-01-06                       267300.373571  ...   \n", "2017-01-13                       288839.907143  ...   \n", "2017-01-20                       315698.407143  ...   \n", "2017-01-27                       330481.266429  ...   \n", "2017-02-03                       333616.898571  ...   \n", "...                                        ...  ...   \n", "2025-05-09                       334095.405238  ...   \n", "2025-05-16                       332477.492381  ...   \n", "2025-05-23                       328498.724762  ...   \n", "2025-05-30                       330760.840952  ...   \n", "2025-06-06                       331316.581429  ...   \n", "\n", "            Total Crude Land + Water.1  Total Products Land + Water.1  \\\n", "Week                                                                    \n", "2017-01-06                         NaN                            NaN   \n", "2017-01-13                         NaN                            NaN   \n", "2017-01-20                         NaN                            NaN   \n", "2017-01-27                         NaN                            NaN   \n", "2017-02-03                  365.186117                    7966.061556   \n", "...                                ...                            ...   \n", "2025-05-09                 2600.023447                     120.800187   \n", "2025-05-16                 1688.424964                      53.511871   \n", "2025-05-23                 1032.080396                    -192.218146   \n", "2025-05-30                 1739.923029                     494.729864   \n", "2025-06-06                  434.945509                     935.427466   \n", "\n", "            Total Global Commercial Liquids on Land.1  \\\n", "Week                                                    \n", "2017-01-06                                        NaN   \n", "2017-01-13                                        NaN   \n", "2017-01-20                                        NaN   \n", "2017-01-27                                        NaN   \n", "2017-02-03                                1701.339536   \n", "...                                               ...   \n", "2025-05-09                                2753.069893   \n", "2025-05-16                                3071.906393   \n", "2025-05-23                                2572.486536   \n", "2025-05-30                                2919.415750   \n", "2025-06-06                                1489.466750   \n", "\n", "            Total Global Commercial Crude Oil.1  Total Global Products Land.1  \\\n", "Week                                                                            \n", "2017-01-06                                  NaN                           NaN   \n", "2017-01-13                                  NaN                           NaN   \n", "2017-01-20                                  NaN                           NaN   \n", "2017-01-27                                  NaN                           NaN   \n", "2017-02-03                          1652.678821                     48.660714   \n", "...                                         ...                           ...   \n", "2025-05-09                          2563.762750                    189.307143   \n", "2025-05-16                          2745.399250                    326.507143   \n", "2025-05-23                          2390.897250                    181.589286   \n", "2025-05-30                          2091.372893                    828.042857   \n", "2025-06-06                           358.356036                   1131.110714   \n", "\n", "            Total Global Core Products Land + Water.1  \\\n", "Week                                                    \n", "2017-01-06                                        NaN   \n", "2017-01-13                                        NaN   \n", "2017-01-20                                        NaN   \n", "2017-01-27                                        NaN   \n", "2017-02-03                                7314.749005   \n", "...                                               ...   \n", "2025-05-09                                -614.870527   \n", "2025-05-16                                -479.884133   \n", "2025-05-23                                -544.774796   \n", "2025-05-30                                 -29.885323   \n", "2025-06-06                                 275.858673   \n", "\n", "            Total Global Gasoline Land + Water.1  \\\n", "Week                                               \n", "2017-01-06                                   NaN   \n", "2017-01-13                                   NaN   \n", "2017-01-20                                   NaN   \n", "2017-01-27                                   NaN   \n", "2017-02-03                           2368.447321   \n", "...                                          ...   \n", "2025-05-09                           -382.404864   \n", "2025-05-16                           -203.277092   \n", "2025-05-23                           -310.257160   \n", "2025-05-30                           -183.703418   \n", "2025-06-06                            -99.243707   \n", "\n", "            Total Global Distillate Land + Water.1  \\\n", "Week                                                 \n", "2017-01-06                                     NaN   \n", "2017-01-13                                     NaN   \n", "2017-01-20                                     NaN   \n", "2017-01-27                                     NaN   \n", "2017-02-03                             2097.133087   \n", "...                                            ...   \n", "2025-05-09                             -428.782976   \n", "2025-05-16                             -213.833486   \n", "2025-05-23                             -150.142942   \n", "2025-05-30                               46.202602   \n", "2025-06-06                              138.046786   \n", "\n", "            Total Global Core Products Land.1  \\\n", "Week                                            \n", "2017-01-06                                NaN   \n", "2017-01-13                                NaN   \n", "2017-01-20                                NaN   \n", "2017-01-27                                NaN   \n", "2017-02-03                         929.103571   \n", "...                                       ...   \n", "2025-05-09                        -691.121429   \n", "2025-05-16                        -398.328571   \n", "2025-05-23                        -391.835714   \n", "2025-05-30                         130.207143   \n", "2025-06-06                         380.146429   \n", "\n", "            Total Global Commercial Crude Oil + Core Products Land  \n", "Week                                                                \n", "2017-01-06                                                NaN       \n", "2017-01-13                                                NaN       \n", "2017-01-20                                                NaN       \n", "2017-01-27                                                NaN       \n", "2017-02-03                                        2581.782393       \n", "...                                                       ...       \n", "2025-05-09                                        1872.641321       \n", "2025-05-16                                        2347.070679       \n", "2025-05-23                                        1999.061536       \n", "2025-05-30                                        2221.580036       \n", "2025-06-06                                         738.502464       \n", "\n", "[440 rows x 62 columns]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["df_merge"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["df_merge_5yr_HiLo = pd.read_excel('merge 5yr HiLo.xlsx')\n", "df_merge_5yr_HiLo = df_merge_5yr_HiLo.set_index('Week')"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Total Land + Water</th>\n", "      <th>Total Crude Land + Water</th>\n", "      <th>Total Products Land + Water</th>\n", "      <th>Total Crude / Core Products Land + Water</th>\n", "      <th>Total Global Commercial Liquids on Land</th>\n", "      <th>Total Crude / Core Products on Land</th>\n", "      <th>Total Global Commercial Crude Oil</th>\n", "      <th>Total Global Products</th>\n", "      <th>Total Global Core Products Land + Water</th>\n", "      <th>Total Global Gasoline</th>\n", "      <th>...</th>\n", "      <th>Total Products.4</th>\n", "      <th>Gasoline.4</th>\n", "      <th>Distillate.4</th>\n", "      <th>Unnamed: 142</th>\n", "      <th>Unnamed: 143</th>\n", "      <th>Total.5</th>\n", "      <th>Crude/Co.5</th>\n", "      <th>Total Products.5</th>\n", "      <th>Gasoline.5</th>\n", "      <th>Distillate.5</th>\n", "    </tr>\n", "    <tr>\n", "      <th>Week</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2024-01-05</th>\n", "      <td>6.404177e+06</td>\n", "      <td>5.125824e+06</td>\n", "      <td>1.278353e+06</td>\n", "      <td>5.936037e+06</td>\n", "      <td>4.800593e+06</td>\n", "      <td>4.423406e+06</td>\n", "      <td>3.895368e+06</td>\n", "      <td>905224.50</td>\n", "      <td>810212.972286</td>\n", "      <td>352554.770714</td>\n", "      <td>...</td>\n", "      <td>8323.73</td>\n", "      <td>1323.75</td>\n", "      <td>3424.92</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.471237e+06</td>\n", "      <td>1.125322e+06</td>\n", "      <td>330548.110000</td>\n", "      <td>73405.075000</td>\n", "      <td>104424.490714</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-12</th>\n", "      <td>6.412294e+06</td>\n", "      <td>5.130237e+06</td>\n", "      <td>1.282058e+06</td>\n", "      <td>5.947942e+06</td>\n", "      <td>4.815432e+06</td>\n", "      <td>4.442795e+06</td>\n", "      <td>3.906746e+06</td>\n", "      <td>908685.42</td>\n", "      <td>817705.517714</td>\n", "      <td>357439.935714</td>\n", "      <td>...</td>\n", "      <td>11069.93</td>\n", "      <td>1228.40</td>\n", "      <td>4145.07</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.472321e+06</td>\n", "      <td>1.126808e+06</td>\n", "      <td>331336.625000</td>\n", "      <td>71999.752857</td>\n", "      <td>104771.052143</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-19</th>\n", "      <td>6.398012e+06</td>\n", "      <td>5.120890e+06</td>\n", "      <td>1.277122e+06</td>\n", "      <td>5.941509e+06</td>\n", "      <td>4.808654e+06</td>\n", "      <td>4.443160e+06</td>\n", "      <td>3.903337e+06</td>\n", "      <td>905316.53</td>\n", "      <td>820619.292857</td>\n", "      <td>360647.200857</td>\n", "      <td>...</td>\n", "      <td>10556.95</td>\n", "      <td>1597.09</td>\n", "      <td>4568.30</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.451622e+06</td>\n", "      <td>1.107428e+06</td>\n", "      <td>332063.161429</td>\n", "      <td>75220.038571</td>\n", "      <td>105215.895000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-01-26</th>\n", "      <td>6.392809e+06</td>\n", "      <td>5.119991e+06</td>\n", "      <td>1.272818e+06</td>\n", "      <td>5.942701e+06</td>\n", "      <td>4.804881e+06</td>\n", "      <td>4.444918e+06</td>\n", "      <td>3.904392e+06</td>\n", "      <td>900489.27</td>\n", "      <td>822710.138714</td>\n", "      <td>363644.162286</td>\n", "      <td>...</td>\n", "      <td>9658.96</td>\n", "      <td>1887.70</td>\n", "      <td>4170.38</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.437239e+06</td>\n", "      <td>1.094056e+06</td>\n", "      <td>331867.245714</td>\n", "      <td>77365.688571</td>\n", "      <td>108498.303571</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2024-02-02</th>\n", "      <td>6.381883e+06</td>\n", "      <td>5.109591e+06</td>\n", "      <td>1.272291e+06</td>\n", "      <td>5.937423e+06</td>\n", "      <td>4.795520e+06</td>\n", "      <td>4.440819e+06</td>\n", "      <td>3.896097e+06</td>\n", "      <td>899422.72</td>\n", "      <td>827831.688429</td>\n", "      <td>366019.833857</td>\n", "      <td>...</td>\n", "      <td>8614.35</td>\n", "      <td>953.62</td>\n", "      <td>4370.19</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.434287e+06</td>\n", "      <td>1.094881e+06</td>\n", "      <td>330193.046429</td>\n", "      <td>77160.443571</td>\n", "      <td>111082.282857</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 148 columns</p>\n", "</div>"], "text/plain": ["            Total Land + Water  Total Crude Land + Water  \\\n", "Week                                                       \n", "2024-01-05        6.404177e+06              5.125824e+06   \n", "2024-01-12        6.412294e+06              5.130237e+06   \n", "2024-01-19        6.398012e+06              5.120890e+06   \n", "2024-01-26        6.392809e+06              5.119991e+06   \n", "2024-02-02        6.381883e+06              5.109591e+06   \n", "\n", "            Total Products Land + Water  \\\n", "Week                                      \n", "2024-01-05                 1.278353e+06   \n", "2024-01-12                 1.282058e+06   \n", "2024-01-19                 1.277122e+06   \n", "2024-01-26                 1.272818e+06   \n", "2024-02-02                 1.272291e+06   \n", "\n", "            Total Crude / Core Products Land + Water  \\\n", "Week                                                   \n", "2024-01-05                              5.936037e+06   \n", "2024-01-12                              5.947942e+06   \n", "2024-01-19                              5.941509e+06   \n", "2024-01-26                              5.942701e+06   \n", "2024-02-02                              5.937423e+06   \n", "\n", "            Total Global Commercial Liquids on Land  \\\n", "Week                                                  \n", "2024-01-05                             4.800593e+06   \n", "2024-01-12                             4.815432e+06   \n", "2024-01-19                             4.808654e+06   \n", "2024-01-26                             4.804881e+06   \n", "2024-02-02                             4.795520e+06   \n", "\n", "            Total Crude / Core Products on Land  \\\n", "Week                                              \n", "2024-01-05                         4.423406e+06   \n", "2024-01-12                         4.442795e+06   \n", "2024-01-19                         4.443160e+06   \n", "2024-01-26                         4.444918e+06   \n", "2024-02-02                         4.440819e+06   \n", "\n", "            Total Global Commercial Crude Oil  Total Global Products  \\\n", "Week                                                                   \n", "2024-01-05                       3.895368e+06              905224.50   \n", "2024-01-12                       3.906746e+06              908685.42   \n", "2024-01-19                       3.903337e+06              905316.53   \n", "2024-01-26                       3.904392e+06              900489.27   \n", "2024-02-02                       3.896097e+06              899422.72   \n", "\n", "            Total Global Core Products Land + Water  Total Global Gasoline  \\\n", "Week                                                                         \n", "2024-01-05                            810212.972286          352554.770714   \n", "2024-01-12                            817705.517714          357439.935714   \n", "2024-01-19                            820619.292857          360647.200857   \n", "2024-01-26                            822710.138714          363644.162286   \n", "2024-02-02                            827831.688429          366019.833857   \n", "\n", "            ...  Total Products.4  Gasoline.4  Distillate.4  Unnamed: 142  \\\n", "Week        ...                                                             \n", "2024-01-05  ...           8323.73     1323.75       3424.92           NaN   \n", "2024-01-12  ...          11069.93     1228.40       4145.07           NaN   \n", "2024-01-19  ...          10556.95     1597.09       4568.30           NaN   \n", "2024-01-26  ...           9658.96     1887.70       4170.38           NaN   \n", "2024-02-02  ...           8614.35      953.62       4370.19           NaN   \n", "\n", "            Unnamed: 143       Total.5    Crude/Co.5  Total Products.5  \\\n", "Week                                                                     \n", "2024-01-05           NaN  1.471237e+06  1.125322e+06     330548.110000   \n", "2024-01-12           NaN  1.472321e+06  1.126808e+06     331336.625000   \n", "2024-01-19           NaN  1.451622e+06  1.107428e+06     332063.161429   \n", "2024-01-26           NaN  1.437239e+06  1.094056e+06     331867.245714   \n", "2024-02-02           NaN  1.434287e+06  1.094881e+06     330193.046429   \n", "\n", "              Gasoline.5   Distillate.5  \n", "Week                                     \n", "2024-01-05  73405.075000  104424.490714  \n", "2024-01-12  71999.752857  104771.052143  \n", "2024-01-19  75220.038571  105215.895000  \n", "2024-01-26  77365.688571  108498.303571  \n", "2024-02-02  77160.443571  111082.282857  \n", "\n", "[5 rows x 148 columns]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["df_merge_5yr_HiLo.head()"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Days in Month</th>\n", "      <th>Week</th>\n", "      <th>Month</th>\n", "      <th>Total Land + Water</th>\n", "      <th>Total Crude Land + Water</th>\n", "      <th>Total Products Land + Water</th>\n", "      <th>Total Global Commercial Liquids on Land</th>\n", "      <th>Total Global Commercial Crude Oil</th>\n", "      <th>Total Global Products</th>\n", "      <th>Total Global Gasoline</th>\n", "      <th>...</th>\n", "      <th>Total Products Floating Storage.1</th>\n", "      <th>Gasoline Floating Storage.1</th>\n", "      <th>Distillate Floating Storage.1</th>\n", "      <th>Unnamed: 81</th>\n", "      <th>Unnamed: 82</th>\n", "      <th>Total Oil on Water.1</th>\n", "      <th>Crude/Co Oil on Water.1</th>\n", "      <th>Total Products Oil on Water.1</th>\n", "      <th>Gasoline Oil on Water.1</th>\n", "      <th>Distillate Oil on Water.1</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>31</td>\n", "      <td>2017-02-03</td>\n", "      <td>2017-01-01</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>2368.44</td>\n", "      <td>304.67</td>\n", "      <td>771.72</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.325043e+06</td>\n", "      <td>1.076410e+06</td>\n", "      <td>248633.343571</td>\n", "      <td>54731.828571</td>\n", "      <td>57232.264286</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>28</td>\n", "      <td>2017-03-03</td>\n", "      <td>2017-02-01</td>\n", "      <td>2783.412663</td>\n", "      <td>1610.104985</td>\n", "      <td>1173.307679</td>\n", "      <td>1504.004679</td>\n", "      <td>2190.201107</td>\n", "      <td>-686.196429</td>\n", "      <td>339.440561</td>\n", "      <td>...</td>\n", "      <td>5538.37</td>\n", "      <td>2095.40</td>\n", "      <td>579.48</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.357609e+06</td>\n", "      <td>1.060079e+06</td>\n", "      <td>297529.528571</td>\n", "      <td>69478.234286</td>\n", "      <td>67890.252857</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>31</td>\n", "      <td>2017-03-31</td>\n", "      <td>2017-03-01</td>\n", "      <td>-652.564498</td>\n", "      <td>-711.822447</td>\n", "      <td>59.257949</td>\n", "      <td>140.448452</td>\n", "      <td>465.004903</td>\n", "      <td>-324.556452</td>\n", "      <td>-252.661037</td>\n", "      <td>...</td>\n", "      <td>9760.66</td>\n", "      <td>3451.74</td>\n", "      <td>930.02</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.340646e+06</td>\n", "      <td>1.035440e+06</td>\n", "      <td>305205.485000</td>\n", "      <td>70258.552143</td>\n", "      <td>68092.513571</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>30</td>\n", "      <td>2017-04-28</td>\n", "      <td>2017-04-01</td>\n", "      <td>37.158571</td>\n", "      <td>-60.088643</td>\n", "      <td>97.247214</td>\n", "      <td>891.348833</td>\n", "      <td>753.093833</td>\n", "      <td>138.255000</td>\n", "      <td>49.480714</td>\n", "      <td>...</td>\n", "      <td>11156.77</td>\n", "      <td>3377.85</td>\n", "      <td>2107.60</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.322071e+06</td>\n", "      <td>1.019492e+06</td>\n", "      <td>302579.141429</td>\n", "      <td>68118.813571</td>\n", "      <td>69247.119286</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>31</td>\n", "      <td>2017-06-02</td>\n", "      <td>2017-05-01</td>\n", "      <td>996.460871</td>\n", "      <td>150.731585</td>\n", "      <td>845.729286</td>\n", "      <td>-781.687194</td>\n", "      <td>-1116.229129</td>\n", "      <td>334.541935</td>\n", "      <td>-10.994816</td>\n", "      <td>...</td>\n", "      <td>14946.52</td>\n", "      <td>3829.21</td>\n", "      <td>2669.56</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.357499e+06</td>\n", "      <td>1.042862e+06</td>\n", "      <td>314636.199286</td>\n", "      <td>72173.614286</td>\n", "      <td>75018.182857</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 88 columns</p>\n", "</div>"], "text/plain": ["   Days in Month       Week      Month  Total Land + Water  \\\n", "0             31 2017-02-03 2017-01-01                 NaN   \n", "1             28 2017-03-03 2017-02-01         2783.412663   \n", "2             31 2017-03-31 2017-03-01         -652.564498   \n", "3             30 2017-04-28 2017-04-01           37.158571   \n", "4             31 2017-06-02 2017-05-01          996.460871   \n", "\n", "   Total Crude Land + Water  Total Products Land + Water  \\\n", "0                       NaN                          NaN   \n", "1               1610.104985                  1173.307679   \n", "2               -711.822447                    59.257949   \n", "3                -60.088643                    97.247214   \n", "4                150.731585                   845.729286   \n", "\n", "   Total Global Commercial Liquids on Land  Total Global Commercial Crude Oil  \\\n", "0                                      NaN                                NaN   \n", "1                              1504.004679                        2190.201107   \n", "2                               140.448452                         465.004903   \n", "3                               891.348833                         753.093833   \n", "4                              -781.687194                       -1116.229129   \n", "\n", "   Total Global Products  Total Global Gasoline  ...  \\\n", "0                    NaN                    NaN  ...   \n", "1            -686.196429             339.440561  ...   \n", "2            -324.556452            -252.661037  ...   \n", "3             138.255000              49.480714  ...   \n", "4             334.541935             -10.994816  ...   \n", "\n", "   Total Products Floating Storage.1  Gasoline Floating Storage.1  \\\n", "0                            2368.44                       304.67   \n", "1                            5538.37                      2095.40   \n", "2                            9760.66                      3451.74   \n", "3                           11156.77                      3377.85   \n", "4                           14946.52                      3829.21   \n", "\n", "   Distillate Floating Storage.1  Unnamed: 81  Unnamed: 82  \\\n", "0                         771.72          NaN          NaN   \n", "1                         579.48          NaN          NaN   \n", "2                         930.02          NaN          NaN   \n", "3                        2107.60          NaN          NaN   \n", "4                        2669.56          NaN          NaN   \n", "\n", "   Total Oil on Water.1  Crude/Co Oil on Water.1  \\\n", "0          1.325043e+06             1.076410e+06   \n", "1          1.357609e+06             1.060079e+06   \n", "2          1.340646e+06             1.035440e+06   \n", "3          1.322071e+06             1.019492e+06   \n", "4          1.357499e+06             1.042862e+06   \n", "\n", "   Total Products Oil on Water.1  Gasoline Oil on Water.1  \\\n", "0                  248633.343571             54731.828571   \n", "1                  297529.528571             69478.234286   \n", "2                  305205.485000             70258.552143   \n", "3                  302579.141429             68118.813571   \n", "4                  314636.199286             72173.614286   \n", "\n", "   Distillate Oil on Water.1  \n", "0               57232.264286  \n", "1               67890.252857  \n", "2               68092.513571  \n", "3               69247.119286  \n", "4               75018.182857  \n", "\n", "[5 rows x 88 columns]"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["df_monthly = pd.read_excel('monthly.xlsx')\n", "df_monthly.head()"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Week</th>\n", "      <th>Total Land + Water</th>\n", "      <th>Total Crude Land + Water</th>\n", "      <th>Total Products Land + Water</th>\n", "      <th>Total Crude / Core Products Land + Water</th>\n", "      <th>Total Global Commercial Liquids on Land</th>\n", "      <th>Total Crude / Core Products on Land</th>\n", "      <th>Total Global Commercial Crude Oil</th>\n", "      <th>Total Global Products Land</th>\n", "      <th>Total Global Core Products Land + Water</th>\n", "      <th>...</th>\n", "      <th>Total Crude Land + Water.1</th>\n", "      <th>Total Products Land + Water.1</th>\n", "      <th>Total Global Commercial Liquids on Land.1</th>\n", "      <th>Total Global Commercial Crude Oil.1</th>\n", "      <th>Total Global Products Land.1</th>\n", "      <th>Total Global Core Products Land + Water.1</th>\n", "      <th>Total Global Gasoline Land + Water.1</th>\n", "      <th>Total Global Distillate Land + Water.1</th>\n", "      <th>Total Global Core Products Land.1</th>\n", "      <th>Total Global Commercial Crude Oil + Core Products Land</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2017-01-06</td>\n", "      <td>6.121479e+06</td>\n", "      <td>5.161332e+06</td>\n", "      <td>9.601464e+05</td>\n", "      <td>5.762742e+06</td>\n", "      <td>4919051.441</td>\n", "      <td>4564548.941</td>\n", "      <td>3988219.641</td>\n", "      <td>930831.80</td>\n", "      <td>601409.964286</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2017-01-13</td>\n", "      <td>6.188136e+06</td>\n", "      <td>5.156263e+06</td>\n", "      <td>1.031873e+06</td>\n", "      <td>5.825710e+06</td>\n", "      <td>4923051.796</td>\n", "      <td>4576455.496</td>\n", "      <td>3992746.746</td>\n", "      <td>930305.05</td>\n", "      <td>669447.146429</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2017-01-20</td>\n", "      <td>6.256074e+06</td>\n", "      <td>5.139623e+06</td>\n", "      <td>1.116451e+06</td>\n", "      <td>5.883212e+06</td>\n", "      <td>4928921.230</td>\n", "      <td>4585184.430</td>\n", "      <td>3986745.380</td>\n", "      <td>942175.85</td>\n", "      <td>743589.086429</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2017-01-27</td>\n", "      <td>6.331286e+06</td>\n", "      <td>5.167375e+06</td>\n", "      <td>1.163911e+06</td>\n", "      <td>5.952881e+06</td>\n", "      <td>4953389.508</td>\n", "      <td>4614433.508</td>\n", "      <td>4009732.058</td>\n", "      <td>943657.45</td>\n", "      <td>785505.622857</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2017-02-03</td>\n", "      <td>6.354753e+06</td>\n", "      <td>5.171557e+06</td>\n", "      <td>1.183196e+06</td>\n", "      <td>5.977780e+06</td>\n", "      <td>4966688.948</td>\n", "      <td>4636838.848</td>\n", "      <td>4034494.648</td>\n", "      <td>932194.30</td>\n", "      <td>806222.936429</td>\n", "      <td>...</td>\n", "      <td>365.186117</td>\n", "      <td>7966.061556</td>\n", "      <td>1701.339536</td>\n", "      <td>1652.678821</td>\n", "      <td>48.660714</td>\n", "      <td>7314.749005</td>\n", "      <td>2368.447321</td>\n", "      <td>2097.133087</td>\n", "      <td>929.103571</td>\n", "      <td>2581.782393</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>435</th>\n", "      <td>2025-05-09</td>\n", "      <td>6.311178e+06</td>\n", "      <td>5.029070e+06</td>\n", "      <td>1.282109e+06</td>\n", "      <td>5.820714e+06</td>\n", "      <td>4737987.422</td>\n", "      <td>4350261.022</td>\n", "      <td>3873301.822</td>\n", "      <td>864685.60</td>\n", "      <td>791644.752381</td>\n", "      <td>...</td>\n", "      <td>2600.023447</td>\n", "      <td>120.800187</td>\n", "      <td>2753.069893</td>\n", "      <td>2563.762750</td>\n", "      <td>189.307143</td>\n", "      <td>-614.870527</td>\n", "      <td>-382.404864</td>\n", "      <td>-428.782976</td>\n", "      <td>-691.121429</td>\n", "      <td>1872.641321</td>\n", "    </tr>\n", "    <tr>\n", "      <th>436</th>\n", "      <td>2025-05-16</td>\n", "      <td>6.320712e+06</td>\n", "      <td>5.040090e+06</td>\n", "      <td>1.280622e+06</td>\n", "      <td>5.829608e+06</td>\n", "      <td>4757103.628</td>\n", "      <td>4367245.028</td>\n", "      <td>3889747.878</td>\n", "      <td>867355.75</td>\n", "      <td>789518.385714</td>\n", "      <td>...</td>\n", "      <td>1688.424964</td>\n", "      <td>53.511871</td>\n", "      <td>3071.906393</td>\n", "      <td>2745.399250</td>\n", "      <td>326.507143</td>\n", "      <td>-479.884133</td>\n", "      <td>-203.277092</td>\n", "      <td>-213.833486</td>\n", "      <td>-398.328571</td>\n", "      <td>2347.070679</td>\n", "    </tr>\n", "    <tr>\n", "      <th>437</th>\n", "      <td>2025-05-23</td>\n", "      <td>6.297677e+06</td>\n", "      <td>5.014539e+06</td>\n", "      <td>1.283138e+06</td>\n", "      <td>5.803764e+06</td>\n", "      <td>4747218.662</td>\n", "      <td>4353120.062</td>\n", "      <td>3875806.912</td>\n", "      <td>871411.75</td>\n", "      <td>789225.111905</td>\n", "      <td>...</td>\n", "      <td>1032.080396</td>\n", "      <td>-192.218146</td>\n", "      <td>2572.486536</td>\n", "      <td>2390.897250</td>\n", "      <td>181.589286</td>\n", "      <td>-544.774796</td>\n", "      <td>-310.257160</td>\n", "      <td>-150.142942</td>\n", "      <td>-391.835714</td>\n", "      <td>1999.061536</td>\n", "    </tr>\n", "    <tr>\n", "      <th>438</th>\n", "      <td>2025-05-30</td>\n", "      <td>6.350730e+06</td>\n", "      <td>5.053297e+06</td>\n", "      <td>1.297433e+06</td>\n", "      <td>5.850345e+06</td>\n", "      <td>4778353.048</td>\n", "      <td>4377260.548</td>\n", "      <td>3890786.698</td>\n", "      <td>887566.35</td>\n", "      <td>797047.320952</td>\n", "      <td>...</td>\n", "      <td>1739.923029</td>\n", "      <td>494.729864</td>\n", "      <td>2919.415750</td>\n", "      <td>2091.372893</td>\n", "      <td>828.042857</td>\n", "      <td>-29.885323</td>\n", "      <td>-183.703418</td>\n", "      <td>46.202602</td>\n", "      <td>130.207143</td>\n", "      <td>2221.580036</td>\n", "    </tr>\n", "    <tr>\n", "      <th>439</th>\n", "      <td>2025-06-06</td>\n", "      <td>6.349549e+06</td>\n", "      <td>5.041248e+06</td>\n", "      <td>1.308301e+06</td>\n", "      <td>5.840617e+06</td>\n", "      <td>4779692.491</td>\n", "      <td>4370939.091</td>\n", "      <td>3883335.791</td>\n", "      <td>896356.70</td>\n", "      <td>799368.795238</td>\n", "      <td>...</td>\n", "      <td>434.945509</td>\n", "      <td>935.427466</td>\n", "      <td>1489.466750</td>\n", "      <td>358.356036</td>\n", "      <td>1131.110714</td>\n", "      <td>275.858673</td>\n", "      <td>-99.243707</td>\n", "      <td>138.046786</td>\n", "      <td>380.146429</td>\n", "      <td>738.502464</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>440 rows × 63 columns</p>\n", "</div>"], "text/plain": ["          Week  Total Land + Water  Total Crude Land + Water  \\\n", "0   2017-01-06        6.121479e+06              5.161332e+06   \n", "1   2017-01-13        6.188136e+06              5.156263e+06   \n", "2   2017-01-20        6.256074e+06              5.139623e+06   \n", "3   2017-01-27        6.331286e+06              5.167375e+06   \n", "4   2017-02-03        6.354753e+06              5.171557e+06   \n", "..         ...                 ...                       ...   \n", "435 2025-05-09        6.311178e+06              5.029070e+06   \n", "436 2025-05-16        6.320712e+06              5.040090e+06   \n", "437 2025-05-23        6.297677e+06              5.014539e+06   \n", "438 2025-05-30        6.350730e+06              5.053297e+06   \n", "439 2025-06-06        6.349549e+06              5.041248e+06   \n", "\n", "     Total Products Land + Water  Total Crude / Core Products Land + Water  \\\n", "0                   9.601464e+05                              5.762742e+06   \n", "1                   1.031873e+06                              5.825710e+06   \n", "2                   1.116451e+06                              5.883212e+06   \n", "3                   1.163911e+06                              5.952881e+06   \n", "4                   1.183196e+06                              5.977780e+06   \n", "..                           ...                                       ...   \n", "435                 1.282109e+06                              5.820714e+06   \n", "436                 1.280622e+06                              5.829608e+06   \n", "437                 1.283138e+06                              5.803764e+06   \n", "438                 1.297433e+06                              5.850345e+06   \n", "439                 1.308301e+06                              5.840617e+06   \n", "\n", "     Total Global Commercial Liquids on Land  \\\n", "0                                4919051.441   \n", "1                                4923051.796   \n", "2                                4928921.230   \n", "3                                4953389.508   \n", "4                                4966688.948   \n", "..                                       ...   \n", "435                              4737987.422   \n", "436                              4757103.628   \n", "437                              4747218.662   \n", "438                              4778353.048   \n", "439                              4779692.491   \n", "\n", "     Total Crude / Core Products on Land  Total Global Commercial Crude Oil  \\\n", "0                            4564548.941                        3988219.641   \n", "1                            4576455.496                        3992746.746   \n", "2                            4585184.430                        3986745.380   \n", "3                            4614433.508                        4009732.058   \n", "4                            4636838.848                        4034494.648   \n", "..                                   ...                                ...   \n", "435                          4350261.022                        3873301.822   \n", "436                          4367245.028                        3889747.878   \n", "437                          4353120.062                        3875806.912   \n", "438                          4377260.548                        3890786.698   \n", "439                          4370939.091                        3883335.791   \n", "\n", "     Total Global Products Land  Total Global Core Products Land + Water  ...  \\\n", "0                     930831.80                            601409.964286  ...   \n", "1                     930305.05                            669447.146429  ...   \n", "2                     942175.85                            743589.086429  ...   \n", "3                     943657.45                            785505.622857  ...   \n", "4                     932194.30                            806222.936429  ...   \n", "..                          ...                                      ...  ...   \n", "435                   864685.60                            791644.752381  ...   \n", "436                   867355.75                            789518.385714  ...   \n", "437                   871411.75                            789225.111905  ...   \n", "438                   887566.35                            797047.320952  ...   \n", "439                   896356.70                            799368.795238  ...   \n", "\n", "     Total Crude Land + Water.1  Total Products Land + Water.1  \\\n", "0                           NaN                            NaN   \n", "1                           NaN                            NaN   \n", "2                           NaN                            NaN   \n", "3                           NaN                            NaN   \n", "4                    365.186117                    7966.061556   \n", "..                          ...                            ...   \n", "435                 2600.023447                     120.800187   \n", "436                 1688.424964                      53.511871   \n", "437                 1032.080396                    -192.218146   \n", "438                 1739.923029                     494.729864   \n", "439                  434.945509                     935.427466   \n", "\n", "     Total Global Commercial Liquids on Land.1  \\\n", "0                                          NaN   \n", "1                                          NaN   \n", "2                                          NaN   \n", "3                                          NaN   \n", "4                                  1701.339536   \n", "..                                         ...   \n", "435                                2753.069893   \n", "436                                3071.906393   \n", "437                                2572.486536   \n", "438                                2919.415750   \n", "439                                1489.466750   \n", "\n", "     Total Global Commercial Crude Oil.1  Total Global Products Land.1  \\\n", "0                                    NaN                           NaN   \n", "1                                    NaN                           NaN   \n", "2                                    NaN                           NaN   \n", "3                                    NaN                           NaN   \n", "4                            1652.678821                     48.660714   \n", "..                                   ...                           ...   \n", "435                          2563.762750                    189.307143   \n", "436                          2745.399250                    326.507143   \n", "437                          2390.897250                    181.589286   \n", "438                          2091.372893                    828.042857   \n", "439                           358.356036                   1131.110714   \n", "\n", "     Total Global Core Products Land + Water.1  \\\n", "0                                          NaN   \n", "1                                          NaN   \n", "2                                          NaN   \n", "3                                          NaN   \n", "4                                  7314.749005   \n", "..                                         ...   \n", "435                                -614.870527   \n", "436                                -479.884133   \n", "437                                -544.774796   \n", "438                                 -29.885323   \n", "439                                 275.858673   \n", "\n", "     Total Global Gasoline Land + Water.1  \\\n", "0                                     NaN   \n", "1                                     NaN   \n", "2                                     NaN   \n", "3                                     NaN   \n", "4                             2368.447321   \n", "..                                    ...   \n", "435                           -382.404864   \n", "436                           -203.277092   \n", "437                           -310.257160   \n", "438                           -183.703418   \n", "439                            -99.243707   \n", "\n", "     Total Global Distillate Land + Water.1  \\\n", "0                                       NaN   \n", "1                                       NaN   \n", "2                                       NaN   \n", "3                                       NaN   \n", "4                               2097.133087   \n", "..                                      ...   \n", "435                             -428.782976   \n", "436                             -213.833486   \n", "437                             -150.142942   \n", "438                               46.202602   \n", "439                              138.046786   \n", "\n", "     Total Global Core Products Land.1  \\\n", "0                                  NaN   \n", "1                                  NaN   \n", "2                                  NaN   \n", "3                                  NaN   \n", "4                           929.103571   \n", "..                                 ...   \n", "435                        -691.121429   \n", "436                        -398.328571   \n", "437                        -391.835714   \n", "438                         130.207143   \n", "439                         380.146429   \n", "\n", "     Total Global Commercial Crude Oil + Core Products Land  \n", "0                                                  NaN       \n", "1                                                  NaN       \n", "2                                                  NaN       \n", "3                                                  NaN       \n", "4                                          2581.782393       \n", "..                                                 ...       \n", "435                                        1872.641321       \n", "436                                        2347.070679       \n", "437                                        1999.061536       \n", "438                                        2221.580036       \n", "439                                         738.502464       \n", "\n", "[440 rows x 63 columns]"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["df_merge"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 2}