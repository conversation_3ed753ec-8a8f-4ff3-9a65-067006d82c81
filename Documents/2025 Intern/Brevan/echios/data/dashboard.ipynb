import pandas as pd


import os

#get the current working directory
os.getcwd()

#list all files in the current directory
files = os.listdir()

for f in files:
    print(f)

import openpyxl
df_merge = pd.read_excel('merge.xlsx')

df_merge.head()

def read_excel_with_dynamic_header(file_path, sheet_name=0):
    """
    Read Excel file with automatic header detection.
    Handles empty rows at the beginning and multi-level headers.
    """
    # First read without header to inspect structure
    temp_df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
    
    # Find first non-empty row
    first_data_row = 0
    while temp_df.iloc[first_data_row].isna().all() and first_data_row < len(temp_df):
        first_data_row += 1
    
    # Check if next row might be part of a multi-level header
    if first_data_row + 1 < len(temp_df):
        next_row_empty_count = temp_df.iloc[first_data_row + 1].isna().sum()
        if next_row_empty_count < len(temp_df.columns) * 0.5:  # Less than half empty cells
            # Multi-level header detected
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=[first_data_row, first_data_row + 1])
            print("Multi-level header detected")
        else:
            # Single level header
            df = pd.read_excel(file_path, sheet_name=sheet_name, header=first_data_row)
            print("Single-level header detected")
    else:
        # Single level header
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=first_data_row)
        print("Single-level header detected")
    
    return df



def process_excel_file(file_path, sheet_name=0):
    """
    Complete workflow: read file, inspect data, visualize.
    """
    # Read file with dynamic header detection
    df = read_excel_with_dynamic_header(file_path, sheet_name)
    
    



import openpyxl
df_merge = pd.read_excel('merge.xlsx')

df_merge = df_merge.set_index('Week')

df_merge

df_merge_5yr_HiLo = pd.read_excel('merge 5yr HiLo.xlsx')
df_merge_5yr_HiLo = df_merge_5yr_HiLo.set_index('Week')

df_merge_5yr_HiLo.head()

df_monthly = pd.read_excel('monthly.xlsx')
df_monthly.head()

df_merge

