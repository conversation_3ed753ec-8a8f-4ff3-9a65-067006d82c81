"""
Global Liquids Inventory Data Tracking Dashboard Functions

This module contains all the core functions for processing and analyzing
global liquids inventory data for the dynamic dashboard.

Features:
- Data loading and preprocessing
- 5-year statistical analysis
- Week-of-year percentile calculations
- Anomaly detection
- Drill-down filtering capabilities
"""

import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Union
import warnings
warnings.filterwarnings('ignore')


class InventoryDataProcessor:
    """Main class for processing global liquids inventory data."""
    
    def __init__(self, data_path: str = "data/"):
        """
        Initialize the data processor.
        
        Args:
            data_path (str): Path to the data directory
        """
        self.data_path = data_path
        self.data = {}
        self.product_mapping = self._create_product_mapping()
        self.geography_mapping = self._create_geography_mapping()
        self.storage_mapping = self._create_storage_mapping()
        
    def _create_product_mapping(self) -> Dict[str, List[str]]:
        """Create mapping of product types to column names based on actual data."""
        return {
            'crude_oil': [
                # Crude oil specific columns
                'Total Crude Land + Water', 'Total Global Commercial Crude Oil',
                'US Commercial Crude (EIA)', 'US SPR Crude (EIA)', 'Crude/Co Oil on Water',
                'Crude/Co Floating Storage'
            ],
            'total_products': [
                # Total products columns
                'Total Products Land + Water', 'Total Global Products Land',
                'US Products (EIA)', 'ARA Total Products (PJK)',
                'Sing Total Products (IE)', 'Total Products Oil on Water',
                'Total Products Floating Storage'
            ],
            'gasoline': [
                # Gasoline specific columns
                'Total Global Gasoline Land + Water', 'Total Global Gasoline Land',
                'US Gasoline (EIA)', 'ARA Gasoline (PJK)', 'Gasoline Oil on Water',
                'Gasoline Floating Storage', 'Sing Light Ends (IE)'
            ],
            'distillates': [
                # Distillate specific columns
                'Total Global Distillate Land + Water', 'Total Global Distillate Land',
                'US Distillate (EIA)', 'ARA Distillate (PJK)', 'Sing Middle Distillates (IE)',
                'Distillate Oil on Water', 'Distillate Floating Storage', 'ARA Jet (PJK)'
            ],
            'core_products': [
                # Core products (crude + key products)
                'Total Crude / Core Products Land + Water', 'Total Crude / Core Products on Land',
                'Total Global Core Products Land + Water', 'Total Global Core Products Land',
                'Core Products on Water', 'Core Products Floating'
            ]
        }
    
    def _create_geography_mapping(self) -> Dict[str, List[str]]:
        """Create mapping of geographic regions to column names based on actual data."""
        return {
            'global': [
                # Total/Global columns (from merge_total.xlsx and others)
                'Total Land + Water', 'Total Crude Land + Water', 'Total Products Land + Water',
                'Total Crude / Core Products Land + Water', 'Total Global Commercial Liquids on Land',
                'Total Crude / Core Products on Land', 'Total Global Commercial Crude Oil',
                'Total Global Products Land', 'Total Global Core Products Land + Water',
                'Total Global Gasoline Land + Water', 'Total Global Distillate Land + Water',
                'Total Global Core Products Land', 'Total Global Gasoline Land', 'Total Global Distillate Land',
                'Total Oil on Water', 'Total Floating Storage', 'Total Products Oil on Water',
                'Total Products Floating Storage'
            ],
            'us': [
                # US-specific columns (from merge_land.xlsx)
                'US Commercial Crude (EIA)', 'US SPR Crude (EIA)', 'US Products (EIA)',
                'US Gasoline (EIA)', 'US Distillate (EIA)', 'US Resid (EIA)'
            ],
            'europe': [
                # ARA (Amsterdam-Rotterdam-Antwerp) and Europe columns
                'ARA Total Products (PJK)', 'ARA Gasoline (PJK)', 'ARA Distillate (PJK)',
                'ARA Jet (PJK)', 'ARA Resid (PJK)', 'Kpler Land Europe'
            ],
            'asia': [
                # Singapore and Asia columns
                'Sing Total Products (IE)', 'Sing Light Ends (IE)', 'Sing Middle Distillates (IE)',
                'Kpler Land China', 'Kpler Land SE Asia'
            ]
        }
    
    def _create_storage_mapping(self) -> Dict[str, str]:
        """Create mapping of storage types to file names."""
        return {
            'land': 'merge_land.xlsx',
            'water': 'merge_water.xlsx', 
            'floating': 'merge_floating.xlsx',
            'total': 'merge_total.xlsx'
        }
    
    def load_data(self) -> None:
        """Load all data files into memory."""
        print("Loading inventory data...")
        
        for storage_type, filename in self.storage_mapping.items():
            try:
                filepath = f"{self.data_path}{filename}"
                df = pd.read_excel(filepath)
                
                # Ensure Week column is datetime
                df['Week'] = pd.to_datetime(df['Week'])
                
                # Sort by date
                df = df.sort_values('Week').reset_index(drop=True)
                
                self.data[storage_type] = df
                print(f"✓ Loaded {storage_type}: {df.shape}")
                
            except Exception as e:
                print(f"✗ Error loading {filename}: {e}")
        
        print(f"Data loading complete. Available storage types: {list(self.data.keys())}")
    
    def get_filtered_data(self,
                         product_type: str = 'all',
                         geography: str = 'all',
                         storage_type: str = 'all') -> pd.DataFrame:
        """
        Get filtered data based on drill-down selections.

        Args:
            product_type (str): Product type filter
            geography (str): Geography filter
            storage_type (str): Storage type filter

        Returns:
            pd.DataFrame: Filtered data
        """
        try:
            # Select base dataset based on storage type
            if storage_type == 'all':
                df = self.data['total'].copy()
            else:
                df = self.data.get(storage_type, self.data['total']).copy()

            # Get all available columns except Week
            all_cols = [col for col in df.columns if col != 'Week']
            selected_cols = []

            # Simple keyword-based filtering
            for col in all_cols:
                include_col = True

                # Geography filtering
                if geography == 'us' and 'US' not in col:
                    include_col = False
                elif geography == 'europe' and not any(x in col for x in ['ARA', 'Europe']):
                    include_col = False
                elif geography == 'asia' and not any(x in col for x in ['Sing', 'China', 'SE Asia']):
                    include_col = False
                elif geography == 'global' and not any(x in col for x in ['Total', 'Global']):
                    include_col = False

                # Product filtering
                if product_type == 'crude_oil' and not any(x in col for x in ['Crude', 'crude']):
                    include_col = False
                elif product_type == 'gasoline' and not any(x in col for x in ['Gasoline', 'gasoline', 'Light Ends']):
                    include_col = False
                elif product_type == 'distillates' and not any(x in col for x in ['Distillate', 'distillate', 'Middle Distillates', 'Jet']):
                    include_col = False
                elif product_type == 'total_products' and not any(x in col for x in ['Products', 'products']) or 'Crude' in col:
                    include_col = False
                elif product_type == 'core_products' and not any(x in col for x in ['Core', 'core']):
                    include_col = False

                if include_col:
                    selected_cols.append(col)

            # If no columns selected, use all columns
            if not selected_cols:
                selected_cols = all_cols

            # Return filtered dataframe
            return df[['Week'] + selected_cols]

        except Exception as e:
            print(f"Error in filtering: {e}")
            # Return original data if error
            return self.data['total'].copy()

    def calculate_5year_stats(self,
                             df: pd.DataFrame,
                             column: str,
                             selected_years: List[int] = None) -> pd.DataFrame:
        """
        Calculate 5-year statistics for future projection (always for 2025+).
        This creates the shading bands for future projection.

        Args:
            df (pd.DataFrame): Input data
            column (str): Column to analyze
            selected_years (List[int]): Not used for shading - shading is always based on historical data

        Returns:
            pd.DataFrame: Statistics by week of year for future projection
        """
        if column not in df.columns:
            return pd.DataFrame()

        # Add week of year
        df_work = df.copy()
        df_work['week_of_year'] = df_work['Week'].dt.isocalendar().week
        df_work['year'] = df_work['Week'].dt.year

        # For 5-year shading, always use the most recent 5 years of historical data
        # This creates the projection bands for 2025 and beyond
        current_year = datetime.now().year
        historical_years = [current_year - i for i in range(1, 6)]  # Last 5 years before current

        # Use historical data to create projection bands
        df_historical = df_work[df_work['year'].isin(historical_years)]

        # If not enough historical data, use all available data
        if df_historical.empty or len(df_historical['year'].unique()) < 3:
            df_historical = df_work

        # Calculate statistics by week of year for projection
        stats = df_historical.groupby('week_of_year')[column].agg([
            'min', 'max', 'mean', 'std', 'count'
        ]).reset_index()

        stats.columns = ['week_of_year', 'min_5yr', 'max_5yr', 'avg_5yr', 'std_5yr', 'count']

        return stats

    def get_selected_years_data(self,
                               df: pd.DataFrame,
                               column: str,
                               selected_years: List[int] = None) -> pd.DataFrame:
        """
        Get data for selected years to show as overlay lines.

        Args:
            df (pd.DataFrame): Input data
            column (str): Column to analyze
            selected_years (List[int]): Years to show as overlay lines

        Returns:
            pd.DataFrame: Data for selected years
        """
        if column not in df.columns or not selected_years:
            return pd.DataFrame()

        df_work = df.copy()
        df_work['week_of_year'] = df_work['Week'].dt.isocalendar().week
        df_work['year'] = df_work['Week'].dt.year

        # Filter for selected years
        df_selected = df_work[df_work['year'].isin(selected_years)]

        return df_selected

    def calculate_percentiles(self,
                             df: pd.DataFrame,
                             column: str) -> pd.DataFrame:
        """
        Calculate percentiles for week-of-year ribbon visualization.

        Args:
            df (pd.DataFrame): Input data
            column (str): Column to analyze

        Returns:
            pd.DataFrame: Data with percentile rankings
        """
        if column not in df.columns:
            return pd.DataFrame()

        df_work = df.copy()
        df_work['week_of_year'] = df_work['Week'].dt.isocalendar().week

        # Calculate percentiles for each week of year
        def calc_percentile(group):
            values = group[column].dropna()
            if len(values) == 0:
                return group

            # Calculate percentile for each value within its week-of-year group
            group['percentile'] = group[column].rank(pct=True) * 100
            return group

        result = df_work.groupby('week_of_year').apply(calc_percentile)
        result = result.reset_index(drop=True)

        return result

    def detect_anomalies(self,
                        df: pd.DataFrame,
                        column: str,
                        threshold: float = 0.1) -> pd.DataFrame:
        """
        Detect anomalies (top/bottom percentile values).

        Args:
            df (pd.DataFrame): Input data with percentiles
            column (str): Column to analyze
            threshold (float): Threshold for anomaly detection (0.1 = 10%)

        Returns:
            pd.DataFrame: Data with anomaly flags
        """
        if 'percentile' not in df.columns:
            df = self.calculate_percentiles(df, column)

        df_result = df.copy()

        # Flag anomalies
        df_result['is_anomaly_high'] = df_result['percentile'] >= (100 - threshold * 100)
        df_result['is_anomaly_low'] = df_result['percentile'] <= (threshold * 100)
        df_result['is_anomaly'] = df_result['is_anomaly_high'] | df_result['is_anomaly_low']

        return df_result

    def get_latest_week_data(self,
                            df: pd.DataFrame,
                            column: str) -> Dict:
        """
        Get data for the most recent week.

        Args:
            df (pd.DataFrame): Input data
            column (str): Column to analyze

        Returns:
            Dict: Latest week information
        """
        if df.empty or column not in df.columns:
            return {}

        latest_row = df.loc[df['Week'].idxmax()]

        return {
            'week': latest_row['Week'],
            'value': latest_row[column],
            'week_of_year': latest_row['Week'].isocalendar().week if pd.notna(latest_row['Week']) else None
        }

    def prepare_chart_data(self,
                          product_type: str = 'all',
                          geography: str = 'all',
                          storage_type: str = 'all',
                          selected_years: List[int] = None,
                          main_column: str = None) -> Dict:
        """
        Prepare all data needed for chart visualization.

        Args:
            product_type (str): Product type filter
            geography (str): Geography filter
            storage_type (str): Storage type filter
            selected_years (List[int]): Years to include
            main_column (str): Main column to analyze

        Returns:
            Dict: Complete chart data package
        """
        # Get filtered data
        df = self.get_filtered_data(product_type, geography, storage_type)

        if df.empty:
            return {'error': 'No data available for selected filters'}

        # Auto-select main column if not provided
        if main_column is None:
            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            main_column = numeric_cols[0] if numeric_cols else None

        if main_column is None or main_column not in df.columns:
            return {'error': 'No suitable column found for analysis'}

        # Calculate all required statistics
        stats_5yr = self.calculate_5year_stats(df, main_column, selected_years)  # For future projection shading
        selected_years_data = self.get_selected_years_data(df, main_column, selected_years)  # For overlay lines
        df_with_percentiles = self.calculate_percentiles(df, main_column)
        df_with_anomalies = self.detect_anomalies(df_with_percentiles, main_column)
        latest_data = self.get_latest_week_data(df, main_column)

        return {
            'main_data': df,
            'column': main_column,
            'stats_5yr': stats_5yr,
            'selected_years_data': selected_years_data,
            'selected_years': selected_years,
            'percentile_data': df_with_percentiles,
            'anomaly_data': df_with_anomalies,
            'latest_week': latest_data,
            'available_columns': df.select_dtypes(include=[np.number]).columns.tolist()
        }


def create_main_chart(chart_data: Dict) -> go.Figure:
    """
    Create the main inventory chart with 5-year projection bands and selected year overlays.

    Args:
        chart_data (Dict): Prepared chart data

    Returns:
        go.Figure: Plotly figure
    """
    if 'error' in chart_data:
        fig = go.Figure()
        fig.add_annotation(text=chart_data['error'],
                          xref="paper", yref="paper",
                          x=0.5, y=0.5, showarrow=False)
        return fig

    fig = go.Figure()

    df = chart_data['main_data']
    column = chart_data['column']
    stats_5yr = chart_data['stats_5yr']
    selected_years_data = chart_data.get('selected_years_data', pd.DataFrame())
    selected_years = chart_data.get('selected_years', [])

    # Add 5-year projection range band (light blue fill) - always for 2025+
    if not stats_5yr.empty:
        # Create week dates for 2025 (future projection)
        projection_year = 2025
        week_dates = []
        for week in stats_5yr['week_of_year']:
            try:
                # Create date for the given week of projection year (2025)
                date = datetime.strptime(f'{projection_year}-W{week:02d}-1', "%Y-W%W-%w")
                week_dates.append(date)
            except:
                # Fallback for week 53 or other edge cases
                week_dates.append(datetime(projection_year, 12, 31))

        # Add 5-year min/max projection band
        fig.add_trace(go.Scatter(
            x=week_dates + week_dates[::-1],
            y=stats_5yr['max_5yr'].tolist() + stats_5yr['min_5yr'].tolist()[::-1],
            fill='toself',
            fillcolor='rgba(173, 216, 230, 0.3)',  # Light blue
            line=dict(color='rgba(255,255,255,0)'),
            name='2025 Projection Range',
            hoverinfo='skip'
        ))

        # Add 5-year average projection line (dashed blue)
        fig.add_trace(go.Scatter(
            x=week_dates,
            y=stats_5yr['avg_5yr'],
            mode='lines',
            line=dict(color='blue', dash='dash', width=2),
            name='2025 Projection Average',
            hovertemplate='Week %{x}<br>Projected Avg: %{y:,.0f}<extra></extra>'
        ))

    # Add selected years as overlay dash lines for comparison
    if not selected_years_data.empty and selected_years:
        colors = ['red', 'green', 'orange', 'purple', 'brown']  # Different colors for different years
        for i, year in enumerate(selected_years):
            year_data = selected_years_data[selected_years_data['year'] == year]
            if not year_data.empty:
                color = colors[i % len(colors)]
                fig.add_trace(go.Scatter(
                    x=year_data['Week'],
                    y=year_data[column],
                    mode='lines',
                    line=dict(color=color, dash='dot', width=2),
                    name=f'{year} Data',
                    hovertemplate=f'{year}: %{{x}}<br>Value: %{{y:,.0f}}<extra></extra>'
                ))

    # Add current/all data line (solid black)
    fig.add_trace(go.Scatter(
        x=df['Week'],
        y=df[column],
        mode='lines+markers',
        line=dict(color='black', width=2),
        marker=dict(size=3),
        name='Historical Data',
        hovertemplate='%{x}<br>Value: %{y:,.0f}<extra></extra>'
    ))

    # Update layout
    fig.update_layout(
        title=f'Global Liquids Inventory: {column}<br><sub>Blue shading: 2025 projection | Dotted lines: Selected years comparison</sub>',
        xaxis_title='Date',
        yaxis_title='Inventory Level',
        hovermode='x unified',
        showlegend=True,
        height=500,
        template='plotly_white'
    )

    return fig


def create_percentile_ribbon(chart_data: Dict) -> go.Figure:
    """
    Create the week-of-year percentile ribbon chart.

    Args:
        chart_data (Dict): Prepared chart data

    Returns:
        go.Figure: Plotly figure
    """
    if 'error' in chart_data:
        fig = go.Figure()
        fig.add_annotation(text=chart_data['error'],
                          xref="paper", yref="paper",
                          x=0.5, y=0.5, showarrow=False)
        return fig

    df = chart_data['percentile_data']
    column = chart_data['column']

    if df.empty or 'percentile' not in df.columns:
        fig = go.Figure()
        fig.add_annotation(text="No percentile data available",
                          xref="paper", yref="paper",
                          x=0.5, y=0.5, showarrow=False)
        return fig

    # Create color scale: green (low percentile) to red (high percentile)
    fig = go.Figure()

    # Group by week of year and get latest value for each week
    weekly_data = df.groupby('week_of_year').last().reset_index()

    # Create scatter plot with color mapping
    fig.add_trace(go.Scatter(
        x=weekly_data['week_of_year'],
        y=[1] * len(weekly_data),  # Fixed y-value for ribbon effect
        mode='markers',
        marker=dict(
            size=20,
            color=weekly_data['percentile'],
            colorscale='RdYlGn_r',  # Red-Yellow-Green reversed (red=high, green=low)
            cmin=0,
            cmax=100,
            colorbar=dict(
                title=dict(
                    text="Percentile",
                    side="right"
                )
            ),
            line=dict(width=1, color='white')
        ),
        text=weekly_data['percentile'].round(1),
        textposition="middle center",
        textfont=dict(size=8, color='white'),
        name='Weekly Percentiles',
        hovertemplate='Week %{x}<br>Percentile: %{text}%<extra></extra>'
    ))

    # Update layout
    fig.update_layout(
        title='Week-of-Year Percentile Ranking',
        xaxis_title='Week of Year',
        yaxis=dict(visible=False, range=[0.5, 1.5]),
        height=150,
        showlegend=False,
        template='plotly_white',
        margin=dict(l=50, r=50, t=50, b=50)
    )

    return fig


def create_anomaly_flags(chart_data: Dict) -> go.Figure:
    """
    Create anomaly flag indicators.

    Args:
        chart_data (Dict): Prepared chart data

    Returns:
        go.Figure: Plotly figure with anomaly flags
    """
    if 'error' in chart_data:
        fig = go.Figure()
        fig.add_annotation(text=chart_data['error'],
                          xref="paper", yref="paper",
                          x=0.5, y=0.5, showarrow=False)
        return fig

    df = chart_data['anomaly_data']

    if df.empty or 'is_anomaly' not in df.columns:
        fig = go.Figure()
        fig.add_annotation(text="No anomaly data available",
                          xref="paper", yref="paper",
                          x=0.5, y=0.5, showarrow=False)
        return fig

    fig = go.Figure()

    # Filter for anomalies only
    anomalies = df[df['is_anomaly']].copy()

    if not anomalies.empty:
        # Group by week of year for flag positioning
        weekly_anomalies = anomalies.groupby('week_of_year').agg({
            'is_anomaly_high': 'any',
            'is_anomaly_low': 'any',
            'Week': 'last'
        }).reset_index()

        # Create flag markers
        for _, row in weekly_anomalies.iterrows():
            color = 'red' if row['is_anomaly_high'] else 'orange'
            symbol = 'triangle-up' if row['is_anomaly_high'] else 'triangle-down'
            flag_type = 'High' if row['is_anomaly_high'] else 'Low'

            fig.add_trace(go.Scatter(
                x=[row['week_of_year']],
                y=[1],
                mode='markers',
                marker=dict(
                    symbol=symbol,
                    size=15,
                    color=color,
                    line=dict(width=2, color='white')
                ),
                name=f'{flag_type} Anomaly',
                hovertemplate=f'Week %{{x}}<br>{flag_type} Anomaly Flag<extra></extra>'
            ))

    # Update layout
    fig.update_layout(
        title='Anomaly Flags (Top/Bottom 10%)',
        xaxis_title='Week of Year',
        yaxis=dict(visible=False, range=[0.5, 1.5]),
        height=100,
        showlegend=True,
        template='plotly_white',
        margin=dict(l=50, r=50, t=50, b=30)
    )

    return fig


def get_summary_stats(chart_data: Dict) -> Dict:
    """
    Generate summary statistics for the dashboard.

    Args:
        chart_data (Dict): Prepared chart data

    Returns:
        Dict: Summary statistics
    """
    if 'error' in chart_data:
        return {'error': chart_data['error']}

    df = chart_data['main_data']
    column = chart_data['column']
    latest_data = chart_data['latest_week']
    anomaly_data = chart_data['anomaly_data']

    stats = {}

    # Basic statistics
    if not df.empty and column in df.columns:
        stats['current_value'] = latest_data.get('value', 'N/A')
        stats['current_week'] = latest_data.get('week', 'N/A')
        stats['total_weeks'] = len(df)
        stats['data_range'] = f"{df['Week'].min().strftime('%Y-%m-%d')} to {df['Week'].max().strftime('%Y-%m-%d')}"

        # Calculate recent trends (last 4 weeks)
        recent_data = df.tail(4)
        if len(recent_data) >= 2:
            trend = recent_data[column].iloc[-1] - recent_data[column].iloc[0]
            stats['recent_trend'] = 'Increasing' if trend > 0 else 'Decreasing' if trend < 0 else 'Stable'
            stats['trend_value'] = abs(trend)

        # Anomaly count
        if not anomaly_data.empty and 'is_anomaly' in anomaly_data.columns:
            stats['total_anomalies'] = anomaly_data['is_anomaly'].sum()
            stats['high_anomalies'] = anomaly_data['is_anomaly_high'].sum()
            stats['low_anomalies'] = anomaly_data['is_anomaly_low'].sum()

    return stats
