{"cells": [{"cell_type": "code", "execution_count": 3, "id": "fe4a1422", "metadata": {}, "outputs": [{"data": {"text/plain": ["'/Users/<USER>/Documents/2025 Intern/Brevan/echios/data'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import os\n", "\n", "os.getcwd()"]}, {"cell_type": "code", "execution_count": null, "id": "ec15fac8", "metadata": {}, "outputs": [], "source": ["# data importing\n", "\n", "import pandas as pd\n", "\n", "# Importing merge_total, which is the dataset containing total on land, water, and floating\n", "merge_total = pd.read_excel(\"data/merge_total.xlsx\")\n", "# Importing merge_land, which is the dataset containing total on land\n", "merge_land = pd.read_excel(\"data/merge_land.xlsx\")\n", "# Importing merge_water, which is the dataset containing total on water\n", "merge_water = pd.read_excel(\"data/merge_water.xlsx\")\n", "# Importing merge_floating, which is the dataset containing total on floating\n", "merge_floating = pd.read_excel(\"data/merge_floating.xlsx\")"]}, {"cell_type": "code", "execution_count": null, "id": "abea5b80", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 5}